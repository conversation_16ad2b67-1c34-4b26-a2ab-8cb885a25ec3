#include "Application.h"
#include "Logger.h"
#include "NetworkClient.h"
#include "WebRTCClient.h"
#include "SecurityManager.h"
#include "ScreenCapture.h"
#include "InputHandler.h"
#include "ExternalDependencies.h"
#include <fstream>
#include <sstream>
#include <iterator>
#include <chrono>

Application *Application::s_instance = nullptr;
SERVICE_STATUS Application::s_serviceStatus = {};
SERVICE_STATUS_HANDLE Application::s_serviceStatusHandle = 0;

Application::Application(int argc, char *argv[])
    : m_serviceMode(false), m_port(DEFAULT_PORT), m_logLevel("info"), m_configFile("config.ini"), m_autoStart(false), m_allowRemoteInput(true), m_running(false), m_initialized(false), m_argc(argc), m_argv(argv), m_useWebRTC(false)
{
    s_instance = this;
    parseCommandLine();
}

Application::~Application()
{
    shutdown();
    s_instance = nullptr;
}

Application *Application::instance()
{
    return s_instance;
}

bool Application::initialize()
{
    if (m_initialized)
    {
        return true;
    }

    try
    {
        // Setup logging
        setupLogging();

        LOG_INFO("Initializing SuperBot Client v" + std::string(SUPERBOT_VERSION_STRING));

        // Load configuration
        loadConfiguration();

        // Determine networking protocol
        std::string protocol = getConfigValue("network.protocol", "TCP");
        m_useWebRTC = (protocol == "WebRTC" || protocol == "webrtc");

        // Initialize components
        m_logger = std::make_unique<Logger>();
        m_securityManager = std::make_unique<SecurityManager>();
        m_screenCapture = std::make_unique<ScreenCapture>();
        m_inputHandler = std::make_unique<InputHandler>();

        // Initialize networking based on configuration
        if (m_useWebRTC)
        {
            LOG_INFO("Initializing WebRTC client");
            m_webRtcClient = std::make_unique<WebRTCClient>();

            // Load WebRTC configuration
            WebRTCConfig webrtcConfig = loadWebRTCConfiguration();

            if (!m_webRtcClient->initialize(webrtcConfig))
            {
                LOG_ERROR("Failed to initialize WebRTC client");
                return false;
            }

            setupWebRTCCallbacks();
        }
        else
        {
            LOG_INFO("Initializing TCP network client");
            m_networkClient = std::make_unique<NetworkClient>();

            if (!m_networkClient->initialize(m_port))
            {
                LOG_ERROR("Failed to initialize network client");
                return false;
            }
        }

        // Initialize screen capture
        if (!m_screenCapture->initialize())
        {
            LOG_ERROR("Failed to initialize screen capture");
            return false;
        }

        // Force enable compression for debugging
        LOG_INFO("Force enabling LZ4 compression for debugging");
        m_screenCapture->setCompressionEnabled(true);

        // Optimize for smooth control - high frame rate with adaptive quality
        LOG_INFO("Optimizing screen capture for maximum smoothness and responsiveness");
        m_screenCapture->setFrameRate(60);           // 60 FPS for maximum smoothness
        m_screenCapture->setQuality(75);             // Balanced quality for good performance
        m_screenCapture->setImageScalePercent(0.7f); // 70% scale for optimal balance between quality and performance

        // Enable adaptive features for dynamic optimization
        m_screenCapture->setAdaptiveQuality(true);
        m_screenCapture->setDeltaFramesEnabled(true);

        // Initialize input handler
        if (!m_inputHandler->initialize())
        {
            LOG_ERROR("Failed to initialize input handler");
            return false;
        }

        // Setup screen capture callback to send frames via network
        LOG_ERROR("SETTING UP FRAME READY CALLBACK - THIS SHOULD APPEAR IN LOG");
        m_screenCapture->setFrameReadyCallback([this](const ScreenFrame &frame)
                                               {
            LOG_ERROR("FRAME READY CALLBACK CALLED - data size: " + std::to_string(frame.data.size()));
            if (m_networkClient && m_networkClient->hasActiveConnection()) {
                LOG_ERROR("CLIENT CONNECTED, SENDING FRAME");
                m_networkClient->sendScreenFrame(frame);
            } else {
                LOG_ERROR("NO CLIENT CONNECTED, SKIPPING FRAME");
            } });
        LOG_ERROR("FRAME READY CALLBACK SET SUCCESSFULLY");

        // Setup network callbacks
        m_networkClient->setClientConnectedCallback([this](const std::string &address)
                                                    { onClientConnected(); });

        m_networkClient->setClientDisconnectedCallback([this]()
                                                       { onClientDisconnected(); });

        m_networkClient->setErrorCallback([this](ErrorCode error, const std::string &message)
                                          { onNetworkError(error, message); });

        m_networkClient->setQualityControlCallback([this](const QualityControlMessage &msg)
                                                   { onQualityControlReceived(msg); });

        m_initialized = true;
        LOG_INFO("Application initialized successfully");
        return true;
    }
    catch (const std::exception &e)
    {
        LOG_ERROR("Initialization failed: " + std::string(e.what()));
        return false;
    }
}

void Application::shutdown()
{
    if (!m_initialized)
    {
        return;
    }

    LOG_INFO("Shutting down application");

    m_running = false;

    // Shutdown components in reverse order
    if (m_webRtcClient)
    {
        m_webRtcClient->disconnect();
        m_webRtcClient->cleanup();
        m_webRtcClient.reset();
    }

    if (m_networkClient)
    {
        m_networkClient->shutdown();
        m_networkClient.reset();
    }

    if (m_inputHandler)
    {
        m_inputHandler->shutdown();
        m_inputHandler.reset();
    }

    if (m_screenCapture)
    {
        m_screenCapture->shutdown();
        m_screenCapture.reset();
    }

    if (m_securityManager)
    {
        m_securityManager.reset();
    }

    // Save configuration
    saveConfiguration();

    m_initialized = false;
    LOG_INFO("Application shutdown complete");
}

int Application::run()
{
    if (!m_initialized)
    {
        LOG_ERROR("Application not initialized");
        return 1;
    }

    m_running = true;
    LOG_INFO("Application started");

    try
    {
        if (m_useWebRTC)
        {
            // Start WebRTC connection
            std::string serverAddress = getConfigValue("webrtc.server_address", "localhost");
            if (!m_webRtcClient->connect(serverAddress))
            {
                LOG_ERROR("Failed to connect to WebRTC server");
                return 1;
            }
            LOG_INFO("WebRTC connection initiated - waiting for peer connection");
        }
        else
        {
            // Start TCP network server
            if (!m_networkClient->startServer())
            {
                LOG_ERROR("Failed to start network server");
                return 1;
            }
            LOG_INFO("TCP server started - waiting for client connection");
        }

        // Don't start screen capture yet - wait for client connection
        LOG_INFO("Screen capture ready - waiting for client connection");

        // Main application loop
        while (m_running)
        {
            if (m_useWebRTC)
            {
                // WebRTC doesn't need explicit event processing
                // Events are handled via callbacks
            }
            else
            {
                // Process TCP network events
                m_networkClient->processEvents();
            }

            // Process screen capture
            m_screenCapture->processFrame();

            // Process input events
            m_inputHandler->processEvents();

            // Minimal sleep for smooth control
            Sleep(1);
        }

        LOG_INFO("Application loop ended");
        return 0;
    }
    catch (const std::exception &e)
    {
        LOG_ERROR("Runtime error: " + std::string(e.what()));
        return 1;
    }
}

void Application::requestShutdown()
{
    LOG_INFO("Shutdown requested");
    m_running = false;
}

std::string Application::getConfigValue(const std::string &key, const std::string &defaultValue) const
{
    auto it = m_config.find(key);
    return (it != m_config.end()) ? it->second : defaultValue;
}

void Application::setConfigValue(const std::string &key, const std::string &value)
{
    m_config[key] = value;
}

void Application::parseCommandLine()
{
    for (int i = 1; i < m_argc; i++)
    {
        std::string arg = m_argv[i];

        if (arg == "-s" || arg == "--service")
        {
            m_serviceMode = true;
        }
        else if (arg == "-p" || arg == "--port")
        {
            if (i + 1 < m_argc)
            {
                m_port = std::atoi(m_argv[++i]);
            }
        }
        else if (arg == "-c" || arg == "--config")
        {
            if (i + 1 < m_argc)
            {
                m_configFile = m_argv[++i];
            }
        }
        else if (arg == "-l" || arg == "--log-level")
        {
            if (i + 1 < m_argc)
            {
                m_logLevel = m_argv[++i];
            }
        }
        else if (arg == "--cert")
        {
            if (i + 1 < m_argc)
            {
                m_certificatePath = m_argv[++i];
            }
        }
        else if (arg == "--key")
        {
            if (i + 1 < m_argc)
            {
                m_keyPath = m_argv[++i];
            }
        }
        else if (arg == "--no-input")
        {
            m_allowRemoteInput = false;
        }
        else if (arg == "--auto-start")
        {
            m_autoStart = true;
        }
    }
}

void Application::setupLogging()
{
    Logger *logger = Logger::instance();

    // Set log level
    LogLevel level = LogLevel::LOG_INFO;
    if (m_logLevel == "debug")
        level = LogLevel::LOG_DEBUG;
    else if (m_logLevel == "info")
        level = LogLevel::LOG_INFO;
    else if (m_logLevel == "warning")
        level = LogLevel::LOG_WARNING;
    else if (m_logLevel == "error")
        level = LogLevel::LOG_ERROR;

    logger->setLogLevel(level);

    // Set log file
    std::string logFile = getLogFilePath();
    logger->setLogFile(logFile);

    // Enable console output only in console mode
    logger->enableConsoleOutput(!m_serviceMode);

    // Always enable event log for services
    logger->enableEventLog(true);
}

void Application::loadConfiguration()
{
    std::ifstream file(m_configFile);
    if (!file.is_open())
    {
        LOG_WARNING("Configuration file not found: " + m_configFile);
        return;
    }

    std::string line;
    std::string currentSection;

    while (std::getline(file, line))
    {
        // Remove whitespace
        line.erase(0, line.find_first_not_of(" \t"));
        line.erase(line.find_last_not_of(" \t") + 1);

        // Skip empty lines and comments
        if (line.empty() || line[0] == '#' || line[0] == ';')
        {
            continue;
        }

        // Check for section
        if (line[0] == '[' && line.back() == ']')
        {
            currentSection = line.substr(1, line.length() - 2);
            continue;
        }

        // Parse key=value
        size_t pos = line.find('=');
        if (pos != std::string::npos)
        {
            std::string key = line.substr(0, pos);
            std::string value = line.substr(pos + 1);

            // Remove whitespace
            key.erase(0, key.find_first_not_of(" \t"));
            key.erase(key.find_last_not_of(" \t") + 1);
            value.erase(0, value.find_first_not_of(" \t"));
            value.erase(value.find_last_not_of(" \t") + 1);

            // Add section prefix if we're in a section
            if (!currentSection.empty())
            {
                key = currentSection + "." + key;
            }

            m_config[key] = value;
        }
    }

    LOG_INFO("Configuration loaded from: " + m_configFile);
}

void Application::saveConfiguration()
{
    std::ofstream file(m_configFile);
    if (!file.is_open())
    {
        LOG_ERROR("Failed to save configuration to: " + m_configFile);
        return;
    }

    // Write configuration
    file << "# SuperBot Client Configuration\n";
    file << "[Network]\n";
    file << "Port=" << m_port << "\n";
    file << "\n";
    file << "[Security]\n";
    file << "CertificatePath=" << m_certificatePath << "\n";
    file << "PrivateKeyPath=" << m_keyPath << "\n";
    file << "\n";
    file << "[Features]\n";
    file << "AllowRemoteInput=" << (m_allowRemoteInput ? "true" : "false") << "\n";
    file << "AutoStart=" << (m_autoStart ? "true" : "false") << "\n";

    LOG_INFO("Configuration saved to: " + m_configFile);
}

void Application::onNetworkError(ErrorCode error, const std::string &message)
{
    LOG_ERROR("Network error (" + std::to_string(static_cast<int>(error)) + "): " + message);
}

void Application::onClientConnected()
{
    LOG_INFO("Client connected - starting screen capture");

    // Make sure callback is set before starting capture
    LOG_INFO("Setting up frame ready callback");
    m_screenCapture->setFrameReadyCallback([this](const ScreenFrame &frame)
                                           {
        LOG_DEBUG("Frame ready callback called - data size: " + std::to_string(frame.data.size()));

        if (m_useWebRTC)
        {
            if (m_webRtcClient && m_webRtcClient->isConnected()) {
                LOG_DEBUG("Sending frame via WebRTC");
                m_webRtcClient->sendScreenFrame(frame);
            } else {
                LOG_DEBUG("WebRTC not connected, skipping frame");
            }
        }
        else
        {
            if (m_networkClient && m_networkClient->hasActiveConnection()) {
                LOG_DEBUG("Sending frame via TCP");
                m_networkClient->sendScreenFrame(frame);
            } else {
                LOG_DEBUG("TCP not connected, skipping frame");
            }
        } });
    LOG_INFO("Frame ready callback configured successfully");

    if (m_screenCapture && !m_screenCapture->isCapturing())
    {
        if (m_screenCapture->start())
        {
            LOG_INFO("Screen capture started successfully");
        }
        else
        {
            LOG_ERROR("Failed to start screen capture");
        }
    }
}

void Application::onClientDisconnected()
{
    LOG_INFO("Client disconnected - stopping screen capture");

    if (m_screenCapture && m_screenCapture->isCapturing())
    {
        m_screenCapture->stop();
        LOG_INFO("Screen capture stopped");
    }
}

void Application::onQualityControlReceived(const QualityControlMessage &msg)
{
    LOG_INFO("Received quality control from server:");
    LOG_INFO("  Image scale: " + std::to_string(static_cast<int>(msg.imageScalePercent * 100)) + "%");
    LOG_INFO("  Resize enabled: " + std::string(msg.resizeEnabled ? "true" : "false"));
    LOG_INFO("  Frame rate: " + std::to_string(msg.frameRate) + " fps");
    LOG_INFO("  Compression quality: " + std::to_string(msg.compressionQuality));
    LOG_INFO("  Compression enabled: " + std::string(msg.compressionEnabled ? "true" : "false"));

    // Apply settings to screen capture
    if (m_screenCapture)
    {
        m_screenCapture->setImageScalePercent(msg.imageScalePercent);
        m_screenCapture->setImageResizeEnabled(msg.resizeEnabled);
        m_screenCapture->setFrameRate(msg.frameRate);
        m_screenCapture->setQuality(msg.compressionQuality);

        // Force enable compression for debugging (override server setting)
        LOG_INFO("Server requested compression: " + std::string(msg.compressionEnabled ? "enabled" : "disabled") +
                 " - FORCING ENABLED for debugging");
        m_screenCapture->setCompressionEnabled(true); // Always enable for debugging

        LOG_INFO("Applied quality settings to screen capture (compression forced ON)");
    }

    // Send quality status back to server
    QualityStatusMessage status = {};
    status.currentImageScale = msg.imageScalePercent;
    status.resizeEnabled = msg.resizeEnabled;
    status.currentFrameRate = msg.frameRate;
    status.averageFrameSize = 0; // TODO: Get from screen capture
    status.cpuUsage = 0.0f;      // TODO: Get CPU usage
    status.timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(
                           std::chrono::system_clock::now().time_since_epoch())
                           .count();

    if (m_useWebRTC)
    {
        if (m_webRtcClient && m_webRtcClient->isConnected())
        {
            // Send quality status via WebRTC
            // This would need to be implemented in WebRTCClient
            LOG_INFO("Quality status ready for WebRTC transmission");
        }
    }
    else
    {
        if (m_networkClient && m_networkClient->hasActiveConnection())
        {
            m_networkClient->sendQualityStatus(status);
            LOG_INFO("Sent quality status back to server via TCP");
        }
    }
}

// Windows service implementation stubs
void Application::installService()
{
    // Implementation moved to main.cpp
}

void Application::uninstallService()
{
    // Implementation moved to main.cpp
}

void WINAPI Application::serviceMain(DWORD /*argc*/, LPWSTR * /*argv*/)
{
    // Implementation moved to main.cpp
}

void WINAPI Application::serviceCtrlHandler(DWORD /*ctrl*/)
{
    // Implementation moved to main.cpp
}

// WebRTC specific implementations
WebRTCConfig Application::loadWebRTCConfiguration()
{
    WebRTCConfig config;

    try
    {
        // Load WebRTC configuration from JSON file
        std::ifstream configFile("webrtc_config.json");
        if (configFile.is_open())
        {
            std::string jsonContent((std::istreambuf_iterator<char>(configFile)),
                                   std::istreambuf_iterator<char>());
            nlohmann::json j = nlohmann::json::parse(jsonContent);

            auto webrtc = j["webrtc"];

            // Signaling configuration
            auto signaling = webrtc["signaling"];
            config.signalingServerUrl = signaling.value("server_url", "ws://localhost");
            config.signalingServerPort = signaling.value("server_port", 8080);

            // ICE servers
            auto iceServers = webrtc["ice_servers"];
            for (const auto& server : iceServers)
            {
                config.iceServers.push_back(server["urls"]);
            }

            // Data channels
            auto dataChannels = webrtc["data_channels"];
            config.enableDataChannels = dataChannels.value("enabled", true);

            // Logging
            auto logging = webrtc["logging"];
            config.enableLogging = logging.value("enabled", true);
            config.logLevel = logging.value("level", "Info");

            LOG_INFO("WebRTC configuration loaded from webrtc_config.json");
        }
        else
        {
            // Use default configuration
            config.signalingServerUrl = "ws://localhost";
            config.signalingServerPort = 8080;
            config.iceServers.push_back("stun:stun.l.google.com:19302");
            config.enableDataChannels = true;
            config.enableLogging = true;
            config.logLevel = "Info";

            LOG_WARNING("WebRTC config file not found, using defaults");
        }
    }
    catch (const std::exception& ex)
    {
        LOG_ERROR("Error loading WebRTC configuration: " + std::string(ex.what()));

        // Fallback to defaults
        config.signalingServerUrl = "ws://localhost";
        config.signalingServerPort = 8080;
        config.iceServers.push_back("stun:stun.l.google.com:19302");
        config.enableDataChannels = true;
        config.enableLogging = true;
        config.logLevel = "Info";
    }

    return config;
}

void Application::setupWebRTCCallbacks()
{
    if (!m_webRtcClient)
        return;

    // Set up connection state change callback
    m_webRtcClient->setOnConnectionStateChange([this](WebRTCConnectionState state) {
        onWebRTCConnectionStateChange(state);
    });

    // Set up data channel message callback
    m_webRtcClient->setOnDataChannelMessage([this](const std::vector<uint8_t>& data) {
        onWebRTCDataReceived(data);
    });

    // Set up signaling callbacks
    m_webRtcClient->setOnSignalingMessage([this](const std::string& message) {
        LOG_DEBUG("Received signaling message: " + message);
    });

    m_webRtcClient->setOnIceCandidate([this](const IceCandidate& candidate) {
        LOG_DEBUG("Received ICE candidate: " + candidate.candidate);
    });

    m_webRtcClient->setOnSessionDescription([this](const SessionDescription& description) {
        LOG_DEBUG("Received session description: " + description.type);
    });

    LOG_INFO("WebRTC callbacks configured");
}

void Application::onWebRTCConnectionStateChange(WebRTCConnectionState state)
{
    switch (state)
    {
        case WebRTCConnectionState::CONNECTING:
            LOG_INFO("WebRTC connection state: Connecting");
            break;

        case WebRTCConnectionState::CONNECTED:
            LOG_INFO("WebRTC connection state: Connected");
            onClientConnected(); // Reuse existing logic
            break;

        case WebRTCConnectionState::DISCONNECTED:
            LOG_INFO("WebRTC connection state: Disconnected");
            onClientDisconnected(); // Reuse existing logic
            break;

        case WebRTCConnectionState::FAILED:
            LOG_ERROR("WebRTC connection state: Failed");
            onClientDisconnected();
            break;

        case WebRTCConnectionState::CLOSED:
            LOG_INFO("WebRTC connection state: Closed");
            onClientDisconnected();
            break;
    }
}

void Application::onWebRTCDataReceived(const std::vector<uint8_t>& data)
{
    try
    {
        LOG_DEBUG("Received WebRTC data: " + std::to_string(data.size()) + " bytes");

        // Parse message header to determine message type
        if (data.size() < sizeof(uint16_t))
        {
            LOG_WARNING("Received WebRTC data too small for message header");
            return;
        }

        // Extract message type (first 2 bytes)
        uint16_t messageType = *reinterpret_cast<const uint16_t*>(data.data());

        switch (static_cast<WebRTCMessageType>(messageType))
        {
            case WebRTCMessageType::QualityControl:
                handleWebRTCQualityControl(data);
                break;

            case WebRTCMessageType::Heartbeat:
                LOG_DEBUG("Received WebRTC heartbeat");
                break;

            case WebRTCMessageType::ErrorResponse:
                handleWebRTCError(data);
                break;

            default:
                LOG_WARNING("Unknown WebRTC message type: " + std::to_string(messageType));
                break;
        }
    }
    catch (const std::exception& ex)
    {
        LOG_ERROR("Error processing WebRTC data: " + std::string(ex.what()));
    }
}

void Application::onWebRTCScreenFrameReceived(const ScreenFrame& frame)
{
    LOG_DEBUG("Received WebRTC screen frame: " + std::to_string(frame.Width) + "x" + std::to_string(frame.Height));

    // This would be used if the client receives screen frames from server
    // For now, just log the reception
}

void Application::handleWebRTCQualityControl(const std::vector<uint8_t>& data)
{
    try
    {
        // Skip message header and parse quality control data
        if (data.size() < sizeof(uint16_t) + sizeof(uint32_t) + sizeof(uint64_t))
        {
            LOG_ERROR("WebRTC quality control message too small");
            return;
        }

        // Extract payload (skip header)
        size_t headerSize = sizeof(uint16_t) + sizeof(uint32_t) + sizeof(uint64_t);
        std::string jsonPayload(data.begin() + headerSize, data.end());

        // Parse JSON quality control message
        nlohmann::json j = nlohmann::json::parse(jsonPayload);

        QualityControlMessage msg;
        msg.imageScalePercent = j.value("ResizePercentage", 100) / 100.0f;
        msg.resizeEnabled = j.value("ResizePercentage", 100) != 100;
        msg.frameRate = j.value("FrameRate", 30);
        msg.compressionQuality = j.value("CompressionLevel", 5);
        msg.compressionEnabled = j.value("EnableAdaptiveQuality", true);

        onQualityControlReceived(msg);
    }
    catch (const std::exception& ex)
    {
        LOG_ERROR("Error parsing WebRTC quality control: " + std::string(ex.what()));
    }
}

void Application::handleWebRTCError(const std::vector<uint8_t>& data)
{
    try
    {
        // Extract error message
        size_t headerSize = sizeof(uint16_t) + sizeof(uint32_t) + sizeof(uint64_t);
        if (data.size() > headerSize)
        {
            std::string errorMessage(data.begin() + headerSize, data.end());
            LOG_ERROR("WebRTC error received: " + errorMessage);
        }
    }
    catch (const std::exception& ex)
    {
        LOG_ERROR("Error parsing WebRTC error message: " + std::string(ex.what()));
    }
}
