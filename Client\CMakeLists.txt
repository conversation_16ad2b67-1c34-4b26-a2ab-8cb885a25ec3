cmake_minimum_required(VERSION 3.20)

# Set project name and version
project(SuperBotClient
    VERSION 1.0.0
    DESCRIPTION "SuperBot Remote Desktop Client"
    LANGUAGES CXX
)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# Set build type if not specified
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Debug)
endif()

# Debug configuration
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    add_compile_definitions(
        _DEBUG
        DEBUG
        SUPERBOT_DEBUG
        ENABLE_LOGGING
        ENABLE_DETAILED_LOGGING
        ENABLE_PERFORMANCE_MONITORING
    )
endif()

# Compiler-specific options
if(MSVC)
    # MSVC specific options
    add_compile_options(/W4 /WX- /permissive-)
    add_compile_definitions(
        WIN32_LEAN_AND_MEAN
        NOMINMAX
        _WIN32_WINNT=0x0A00  # Windows 10
        WINVER=0x0A00
        _CRT_SECURE_NO_WARNINGS
        _WINSOCK_DEPRECATED_NO_WARNINGS
    )

    # Release optimizations
    if(CMAKE_BUILD_TYPE STREQUAL "Release")
        add_compile_options(/O2 /Ob2 /DNDEBUG)
    endif()

    # Debug options
    if(CMAKE_BUILD_TYPE STREQUAL "Debug")
        add_compile_options(
            /Od          # Disable optimizations
            /Zi          # Generate debug information
            /RTC1        # Runtime checks
            /MDd         # Debug runtime library
        )
    endif()

elseif(CMAKE_CXX_COMPILER_ID STREQUAL "GNU" OR CMAKE_CXX_COMPILER_ID STREQUAL "Clang")
    # GCC/Clang specific options
    add_compile_options(-Wall -Wextra -Wpedantic)
    add_compile_definitions(
        WIN32_LEAN_AND_MEAN
        NOMINMAX
        _WIN32_WINNT=0x0A00  # Windows 10
        WINVER=0x0A00
    )

    # Release optimizations
    if(CMAKE_BUILD_TYPE STREQUAL "Release")
        add_compile_options(-O3 -DNDEBUG)
    endif()

    # Debug options
    if(CMAKE_BUILD_TYPE STREQUAL "Debug")
        add_compile_options(
            -g3          # Maximum debug information
            -O0          # No optimization
            -ggdb        # GDB-specific debug info
            -fno-omit-frame-pointer  # Keep frame pointers for better debugging
            -fsanitize=address       # Address sanitizer (optional)
            -fsanitize=undefined     # Undefined behavior sanitizer (optional)
        )
        add_link_options(
            -fsanitize=address
            -fsanitize=undefined
        )
    endif()
endif()

# Find required packages (simplified for now)
# Note: In production, these would be properly linked
# For now, we'll create a minimal build that compiles

# Include directories
include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}/include
)

# Source files (simplified for minimal build)
set(SOURCES
    src/main_simple.cpp
)

# Header files (for IDE support)
set(HEADERS
    include/Application.h
    include/Common.h
    include/Compression.h
    include/InputHandler.h
    include/Logger.h
    include/NetworkClient.h
    include/ScreenCapture.h
    include/SecurityManager.h
    include/WebRTCClient.h
    include/SignalingClient.h
)

# Create executable
add_executable(${PROJECT_NAME} ${SOURCES} ${HEADERS})

# Link libraries (Windows system libraries only for now)
target_link_libraries(${PROJECT_NAME} PRIVATE
    ws2_32
    crypt32
    gdi32
    user32
    kernel32
    advapi32
    shell32
    ole32
    oleaut32
    uuid
    comctl32
    comdlg32
)

# Set target properties
set_target_properties(${PROJECT_NAME} PROPERTIES
    OUTPUT_NAME "SuperBotClient"
    RUNTIME_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/bin"
    RUNTIME_OUTPUT_DIRECTORY_DEBUG "${CMAKE_BINARY_DIR}/bin/Debug"
    RUNTIME_OUTPUT_DIRECTORY_RELEASE "${CMAKE_BINARY_DIR}/bin/Release"
)

# Set MSVC runtime library for debug builds
if(MSVC AND CMAKE_BUILD_TYPE STREQUAL "Debug")
    set_property(TARGET ${PROJECT_NAME} PROPERTY
        MSVC_RUNTIME_LIBRARY "MultiThreadedDebugDLL")
endif()

# Windows specific settings
if(WIN32)
    # Set subsystem to console for service application
    set_target_properties(${PROJECT_NAME} PROPERTIES
        WIN32_EXECUTABLE FALSE
    )

    # Add Windows manifest if needed
    if(MSVC)
        set_target_properties(${PROJECT_NAME} PROPERTIES
            LINK_FLAGS "/MANIFEST:NO"
        )
    endif()
endif()

# Install configuration
install(TARGETS ${PROJECT_NAME}
    RUNTIME DESTINATION bin
    COMPONENT Runtime
)

# CPack configuration for creating installer
set(CPACK_PACKAGE_NAME "SuperBot Client")
set(CPACK_PACKAGE_VENDOR "SuperBot Team")
set(CPACK_PACKAGE_DESCRIPTION_SUMMARY "SuperBot Remote Desktop Client")
set(CPACK_PACKAGE_VERSION_MAJOR ${PROJECT_VERSION_MAJOR})
set(CPACK_PACKAGE_VERSION_MINOR ${PROJECT_VERSION_MINOR})
set(CPACK_PACKAGE_VERSION_PATCH ${PROJECT_VERSION_PATCH})
set(CPACK_PACKAGE_INSTALL_DIRECTORY "SuperBot")

if(WIN32)
    set(CPACK_GENERATOR "NSIS;ZIP")
    set(CPACK_NSIS_DISPLAY_NAME "SuperBot Client")
    set(CPACK_NSIS_PACKAGE_NAME "SuperBot Client")
    set(CPACK_NSIS_CONTACT "<EMAIL>")
    set(CPACK_NSIS_HELP_LINK "https://superbot.com/support")
    set(CPACK_NSIS_URL_INFO_ABOUT "https://superbot.com")
    set(CPACK_NSIS_MODIFY_PATH ON)
endif()

include(CPack)

# Print configuration summary
message(STATUS "=== SuperBot Client Configuration ===")
message(STATUS "Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "C++ standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "Compiler: ${CMAKE_CXX_COMPILER_ID}")
message(STATUS "Install prefix: ${CMAKE_INSTALL_PREFIX}")
message(STATUS "=====================================")
