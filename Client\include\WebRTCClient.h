#pragma once

#include "Common.h"
#include <string>
#include <vector>
#include <functional>
#include <memory>
#include <thread>
#include <mutex>
#include <condition_variable>
#include <queue>
#include <atomic>

// Forward declarations for WebRTC types
namespace webrtc {
    class PeerConnectionInterface;
    class DataChannelInterface;
    class PeerConnectionFactoryInterface;
    class CreateSessionDescriptionObserver;
    class SetSessionDescriptionObserver;
}

// WebRTC Message Types
enum class WebRTCMessageType : uint16_t {
    SIGNALING_OFFER = 0x1000,
    SIGNALING_ANSWER = 0x1001,
    SIGNALING_ICE_CANDIDATE = 0x1002,
    DATA_CHANNEL_OPEN = 0x1003,
    DATA_CHANNEL_CLOSE = 0x1004,
    SCREEN_FRAME = 0x1005,
    HEARTBEAT = 0x1006,
    QUALITY_CONTROL = 0x1007,
    ERROR_RESPONSE = 0x1008
};

// WebRTC Connection State
enum class WebRTCConnectionState {
    DISCON<PERSON>CTED,
    CONNECTING,
    CONNECTED,
    FAILED,
    CLOSED
};

// ICE Candidate structure
struct IceCandidate {
    std::string candidate;
    std::string sdpMid;
    int sdpMLineIndex;
};

// Session Description structure
struct SessionDescription {
    std::string type; // "offer" or "answer"
    std::string sdp;
};

// WebRTC Configuration
struct WebRTCConfig {
    std::vector<std::string> iceServers;
    std::string signalingServerUrl;
    int signalingServerPort;
    bool enableDataChannels;
    bool enableLogging;
    std::string logLevel;
};

// Callback function types
using OnConnectionStateChangeCallback = std::function<void(WebRTCConnectionState)>;
using OnDataChannelMessageCallback = std::function<void(const std::vector<uint8_t>&)>;
using OnSignalingMessageCallback = std::function<void(const std::string&)>;
using OnIceCandidateCallback = std::function<void(const IceCandidate&)>;
using OnSessionDescriptionCallback = std::function<void(const SessionDescription&)>;

class WebRTCClient {
public:
    WebRTCClient();
    ~WebRTCClient();

    // Initialization and cleanup
    bool initialize(const WebRTCConfig& config);
    void cleanup();

    // Connection management
    bool connect(const std::string& peerId);
    void disconnect();
    bool isConnected() const;
    WebRTCConnectionState getConnectionState() const;

    // Signaling
    bool createOffer();
    bool createAnswer(const SessionDescription& offer);
    bool setRemoteDescription(const SessionDescription& description);
    bool addIceCandidate(const IceCandidate& candidate);

    // Data channel operations
    bool sendData(const std::vector<uint8_t>& data);
    bool sendScreenFrame(const ScreenFrame& frame);
    bool sendMessage(WebRTCMessageType type, const std::vector<uint8_t>& payload);

    // Callback setters
    void setOnConnectionStateChange(OnConnectionStateChangeCallback callback);
    void setOnDataChannelMessage(OnDataChannelMessageCallback callback);
    void setOnSignalingMessage(OnSignalingMessageCallback callback);
    void setOnIceCandidate(OnIceCandidateCallback callback);
    void setOnSessionDescription(OnSessionDescriptionCallback callback);

    // Statistics
    struct Statistics {
        uint64_t bytesSent;
        uint64_t bytesReceived;
        uint32_t messagesSent;
        uint32_t messagesReceived;
        uint32_t packetsLost;
        double roundTripTime;
        double bandwidth;
    };
    Statistics getStatistics() const;
    void resetStatistics();

private:
    // WebRTC components
    std::shared_ptr<webrtc::PeerConnectionFactoryInterface> m_peerConnectionFactory;
    std::shared_ptr<webrtc::PeerConnectionInterface> m_peerConnection;
    std::shared_ptr<webrtc::DataChannelInterface> m_dataChannel;

    // Configuration
    WebRTCConfig m_config;
    std::string m_peerId;

    // State
    std::atomic<WebRTCConnectionState> m_connectionState;
    std::atomic<bool> m_initialized;

    // Threading
    std::unique_ptr<std::thread> m_signalingThread;
    std::unique_ptr<std::thread> m_networkThread;
    std::atomic<bool> m_running;

    // Synchronization
    mutable std::mutex m_mutex;
    std::condition_variable m_condition;

    // Message queue
    std::queue<std::vector<uint8_t>> m_sendQueue;
    std::mutex m_sendQueueMutex;

    // Callbacks
    OnConnectionStateChangeCallback m_onConnectionStateChange;
    OnDataChannelMessageCallback m_onDataChannelMessage;
    OnSignalingMessageCallback m_onSignalingMessage;
    OnIceCandidateCallback m_onIceCandidate;
    OnSessionDescriptionCallback m_onSessionDescription;

    // Statistics
    mutable std::mutex m_statsMutex;
    Statistics m_statistics;

    // Private methods
    bool initializePeerConnectionFactory();
    bool createPeerConnection();
    bool createDataChannel();
    void setupCallbacks();
    
    // Signaling
    bool connectToSignalingServer();
    void signalingThreadFunction();
    void handleSignalingMessage(const std::string& message);
    
    // Network
    void networkThreadFunction();
    void processSendQueue();
    
    // WebRTC callbacks
    void onConnectionStateChange(webrtc::PeerConnectionInterface::PeerConnectionState state);
    void onDataChannelStateChange();
    void onDataChannelMessage(const std::vector<uint8_t>& data);
    void onIceCandidate(const IceCandidate& candidate);
    void onCreateSessionDescriptionSuccess(const SessionDescription& description);
    void onCreateSessionDescriptionFailure(const std::string& error);
    void onSetSessionDescriptionSuccess();
    void onSetSessionDescriptionFailure(const std::string& error);

    // Utility methods
    std::vector<uint8_t> serializeScreenFrame(const ScreenFrame& frame);
    ScreenFrame deserializeScreenFrame(const std::vector<uint8_t>& data);
    std::string serializeIceCandidate(const IceCandidate& candidate);
    IceCandidate deserializeIceCandidate(const std::string& json);
    std::string serializeSessionDescription(const SessionDescription& description);
    SessionDescription deserializeSessionDescription(const std::string& json);
    
    // Logging
    void logDebug(const std::string& message);
    void logInfo(const std::string& message);
    void logWarning(const std::string& message);
    void logError(const std::string& message);
};
