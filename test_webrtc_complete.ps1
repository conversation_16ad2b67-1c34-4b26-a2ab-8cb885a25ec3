# Complete WebRTC Integration Test Script
# Tests the full WebRTC implementation from client to server

param(
    [switch]$ServerOnly,
    [switch]$ClientOnly,
    [switch]$Verbose,
    [int]$TestDuration = 30
)

$ErrorActionPreference = "Stop"

# Colors for output
$Red = "Red"
$Green = "Green"
$Yellow = "Yellow"
$Blue = "Blue"
$Cyan = "Cyan"

function Write-ColorOutput {
    param([string]$Message, [string]$Color = "White")
    Write-Host $Message -ForegroundColor $Color
}

function Write-Section {
    param([string]$Title)
    Write-Host ""
    Write-ColorOutput "=" * 60 -Color $Cyan
    Write-ColorOutput "  $Title" -Color $Cyan
    Write-ColorOutput "=" * 60 -Color $Cyan
}

function Write-Step {
    param([string]$Message)
    Write-ColorOutput ">>> $Message" -Color $Yellow
}

function Write-Success {
    param([string]$Message)
    Write-ColorOutput "✓ $Message" -Color $Green
}

function Write-Error {
    param([string]$Message)
    Write-ColorOutput "✗ $Message" -Color $Red
}

function Start-ServerTest {
    Write-Section "Starting WebRTC Server Test"
    
    try {
        Write-Step "Building server..."
        Set-Location "Server"
        
        $buildResult = Start-Process -FilePath "dotnet" -ArgumentList "build --configuration Debug" -Wait -PassThru -NoNewWindow
        if ($buildResult.ExitCode -ne 0) {
            Write-Error "Server build failed"
            return $false
        }
        Write-Success "Server built successfully"
        
        Write-Step "Starting WebRTC server..."
        $serverProcess = Start-Process -FilePath "dotnet" -ArgumentList "run --project SuperBotServer" -PassThru
        
        # Wait for server to start
        Start-Sleep -Seconds 5
        
        if ($serverProcess.HasExited) {
            Write-Error "Server process exited unexpectedly"
            return $false
        }
        
        Write-Success "Server started successfully (PID: $($serverProcess.Id))"
        
        # Test server endpoints
        Write-Step "Testing server endpoints..."
        
        try {
            # Test if signaling server is running
            $response = Invoke-WebRequest -Uri "http://localhost:8080" -Method GET -TimeoutSec 5 -ErrorAction SilentlyContinue
            Write-Success "Signaling server is responding"
        }
        catch {
            Write-Error "Signaling server not responding: $($_.Exception.Message)"
        }
        
        # Keep server running for testing
        Write-ColorOutput "Server is running. Press Ctrl+C to stop..." -Color $Blue
        
        # Wait for test duration or user interrupt
        $startTime = Get-Date
        while ((Get-Date) - $startTime -lt [TimeSpan]::FromSeconds($TestDuration)) {
            if ($serverProcess.HasExited) {
                Write-Error "Server process exited unexpectedly"
                break
            }
            Start-Sleep -Seconds 1
        }
        
        # Stop server
        Write-Step "Stopping server..."
        if (!$serverProcess.HasExited) {
            $serverProcess.Kill()
            $serverProcess.WaitForExit(5000)
        }
        Write-Success "Server stopped"
        
        return $true
    }
    catch {
        Write-Error "Server test failed: $($_.Exception.Message)"
        return $false
    }
    finally {
        Set-Location ".."
    }
}

function Start-ClientTest {
    Write-Section "Starting WebRTC Client Test"
    
    try {
        Write-Step "Building client..."
        Set-Location "Client"
        
        # Create build directory if it doesn't exist
        if (!(Test-Path "build")) {
            New-Item -ItemType Directory -Path "build" | Out-Null
        }
        
        Set-Location "build"
        
        # Run CMake configuration
        Write-Step "Configuring CMake..."
        $cmakeResult = Start-Process -FilePath "cmake" -ArgumentList ".." -Wait -PassThru -NoNewWindow
        if ($cmakeResult.ExitCode -ne 0) {
            Write-Error "CMake configuration failed"
            return $false
        }
        Write-Success "CMake configured successfully"
        
        # Build client
        Write-Step "Building client..."
        $buildResult = Start-Process -FilePath "cmake" -ArgumentList "--build . --config Debug" -Wait -PassThru -NoNewWindow
        if ($buildResult.ExitCode -ne 0) {
            Write-Error "Client build failed"
            return $false
        }
        Write-Success "Client built successfully"
        
        # Test client configuration
        Write-Step "Testing client configuration..."
        
        # Create test configuration
        $testConfig = @{
            "network" = @{
                "protocol" = "WebRTC"
            }
            "webrtc" = @{
                "server_address" = "localhost"
            }
        }
        
        $configPath = "../config.ini"
        "[Network]" | Out-File -FilePath $configPath -Encoding UTF8
        "Protocol=WebRTC" | Out-File -FilePath $configPath -Append -Encoding UTF8
        "" | Out-File -FilePath $configPath -Append -Encoding UTF8
        "[WebRTC]" | Out-File -FilePath $configPath -Append -Encoding UTF8
        "ServerAddress=localhost" | Out-File -FilePath $configPath -Append -Encoding UTF8
        
        Write-Success "Client configuration created"
        
        # Note: Actual client execution would require the server to be running
        Write-ColorOutput "Client build completed. To test connection, start server first." -Color $Blue
        
        return $true
    }
    catch {
        Write-Error "Client test failed: $($_.Exception.Message)"
        return $false
    }
    finally {
        Set-Location "../.."
    }
}

function Test-WebRTCIntegration {
    Write-Section "WebRTC Integration Test"
    
    try {
        Write-Step "Running integration tests..."
        Set-Location "Server"
        
        # Run WebRTC integration tests
        $testResult = Start-Process -FilePath "dotnet" -ArgumentList "test SuperBotServer.Tests/WebRTCIntegrationTests.cs --logger console" -Wait -PassThru -NoNewWindow
        
        if ($testResult.ExitCode -eq 0) {
            Write-Success "Integration tests passed"
            return $true
        } else {
            Write-Error "Integration tests failed"
            return $false
        }
    }
    catch {
        Write-Error "Integration test failed: $($_.Exception.Message)"
        return $false
    }
    finally {
        Set-Location ".."
    }
}

function Test-WebRTCExample {
    Write-Section "WebRTC Example Test"
    
    try {
        Write-Step "Running WebRTC example..."
        Set-Location "Server"
        
        # Run WebRTC example (with timeout)
        $exampleProcess = Start-Process -FilePath "dotnet" -ArgumentList "run --project SuperBotServer/Examples/WebRTCExample.cs" -PassThru
        
        # Wait for example to initialize
        Start-Sleep -Seconds 10
        
        if ($exampleProcess.HasExited) {
            if ($exampleProcess.ExitCode -eq 0) {
                Write-Success "WebRTC example completed successfully"
                return $true
            } else {
                Write-Error "WebRTC example failed with exit code: $($exampleProcess.ExitCode)"
                return $false
            }
        } else {
            # Stop the example after test duration
            $exampleProcess.Kill()
            $exampleProcess.WaitForExit(5000)
            Write-Success "WebRTC example ran successfully (stopped after timeout)"
            return $true
        }
    }
    catch {
        Write-Error "WebRTC example test failed: $($_.Exception.Message)"
        return $false
    }
    finally {
        Set-Location ".."
    }
}

function Show-TestSummary {
    param([hashtable]$Results)
    
    Write-Section "Test Summary"
    
    $totalTests = $Results.Count
    $passedTests = ($Results.Values | Where-Object { $_ -eq $true }).Count
    $failedTests = $totalTests - $passedTests
    
    Write-ColorOutput "Total Tests: $totalTests" -Color $Blue
    Write-ColorOutput "Passed: $passedTests" -Color $Green
    Write-ColorOutput "Failed: $failedTests" -Color $Red
    
    Write-Host ""
    Write-ColorOutput "Detailed Results:" -Color $Blue
    foreach ($test in $Results.GetEnumerator()) {
        $status = if ($test.Value) { "PASS" } else { "FAIL" }
        $color = if ($test.Value) { $Green } else { $Red }
        Write-ColorOutput "  $($test.Key): $status" -Color $color
    }
    
    if ($failedTests -eq 0) {
        Write-Host ""
        Write-Success "All tests passed! WebRTC implementation is working correctly."
    } else {
        Write-Host ""
        Write-Error "Some tests failed. Please check the implementation."
    }
}

# Main execution
Write-Section "SuperBot WebRTC Complete Integration Test"

Write-ColorOutput "Test Configuration:" -Color $Blue
Write-ColorOutput "  Server Only: $ServerOnly" -Color $Blue
Write-ColorOutput "  Client Only: $ClientOnly" -Color $Blue
Write-ColorOutput "  Test Duration: $TestDuration seconds" -Color $Blue
Write-ColorOutput "  Verbose: $Verbose" -Color $Blue

$testResults = @{}

try {
    if ($ClientOnly) {
        # Run client tests only
        $testResults["Client Build"] = Start-ClientTest
    }
    elseif ($ServerOnly) {
        # Run server tests only
        $testResults["Server Test"] = Start-ServerTest
        $testResults["Integration Tests"] = Test-WebRTCIntegration
        $testResults["WebRTC Example"] = Test-WebRTCExample
    }
    else {
        # Run all tests
        Write-Step "Running complete WebRTC test suite..."
        
        # Build and test client
        $testResults["Client Build"] = Start-ClientTest
        
        # Test server components
        $testResults["Integration Tests"] = Test-WebRTCIntegration
        $testResults["WebRTC Example"] = Test-WebRTCExample
        
        # Full server test (this should be last as it runs for the full duration)
        $testResults["Server Test"] = Start-ServerTest
    }
}
catch {
    Write-Error "Test execution failed: $($_.Exception.Message)"
}

# Show summary
Show-TestSummary -Results $testResults

# Exit with appropriate code
$exitCode = if (($testResults.Values | Where-Object { $_ -eq $false }).Count -eq 0) { 0 } else { 1 }

Write-Section "Test Completed"
Write-ColorOutput "Exit Code: $exitCode" -Color $(if ($exitCode -eq 0) { $Green } else { $Red })

exit $exitCode
