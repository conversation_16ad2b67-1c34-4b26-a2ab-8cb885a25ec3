#include "SignalingClient.h"
#include "Logger.h"
#include <nlohmann/json.hpp>
#include <thread>
#include <chrono>

using json = nlohmann::json;

SignalingClient::SignalingClient()
    : m_port(0)
    , m_connectionState(ConnectionState::DISCONNECTED)
    , m_running(false)
    , m_websocketHandle(nullptr)
{
}

SignalingClient::~SignalingClient()
{
    disconnect();
}

bool SignalingClient::connect(const std::string& serverUrl, int port, const std::string& peerId)
{
    if (m_connectionState == ConnectionState::CONNECTED)
    {
        logWarning("Already connected to signaling server");
        return true;
    }

    logInfo("Connecting to signaling server: " + serverUrl + ":" + std::to_string(port));

    m_serverUrl = serverUrl;
    m_port = port;
    m_peerId = peerId;

    updateConnectionState(ConnectionState::CONNECTING);

    try
    {
        // Initialize WebSocket connection
        if (!initializeWebSocket())
        {
            logError("Failed to initialize WebSocket connection");
            updateConnectionState(ConnectionState::FAILED);
            return false;
        }

        m_running = true;

        // Start connection thread
        m_connectionThread = std::make_unique<std::thread>(&SignalingClient::connectionThreadFunction, this);
        
        // Start message processing thread
        m_messageThread = std::make_unique<std::thread>(&SignalingClient::messageThreadFunction, this);

        // Wait for connection to be established (with timeout)
        auto startTime = std::chrono::steady_clock::now();
        const auto timeout = std::chrono::seconds(10);

        while (m_connectionState == ConnectionState::CONNECTING)
        {
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
            
            if (std::chrono::steady_clock::now() - startTime > timeout)
            {
                logError("Connection timeout");
                updateConnectionState(ConnectionState::FAILED);
                return false;
            }
        }

        if (m_connectionState == ConnectionState::CONNECTED)
        {
            // Send registration message
            SignalingMessage registerMsg;
            registerMsg.type = MessageType::REGISTER;
            registerMsg.fromPeerId = m_peerId;
            registerMsg.timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(
                std::chrono::system_clock::now().time_since_epoch()).count();

            return sendMessage(registerMsg);
        }

        return false;
    }
    catch (const std::exception& ex)
    {
        logError("Exception during connection: " + std::string(ex.what()));
        updateConnectionState(ConnectionState::FAILED);
        return false;
    }
}

void SignalingClient::disconnect()
{
    if (m_connectionState == ConnectionState::DISCONNECTED)
        return;

    logInfo("Disconnecting from signaling server");

    m_running = false;
    updateConnectionState(ConnectionState::DISCONNECTED);

    // Clean up WebSocket
    cleanupWebSocket();

    // Wait for threads to finish
    if (m_connectionThread && m_connectionThread->joinable())
    {
        m_connectionThread->join();
    }

    if (m_messageThread && m_messageThread->joinable())
    {
        m_messageThread->join();
    }

    // Clear connected peers
    {
        std::lock_guard<std::mutex> lock(m_peersListMutex);
        m_connectedPeers.clear();
    }

    logInfo("Disconnected from signaling server");
}

bool SignalingClient::isConnected() const
{
    return m_connectionState == ConnectionState::CONNECTED;
}

SignalingClient::ConnectionState SignalingClient::getConnectionState() const
{
    return m_connectionState;
}

bool SignalingClient::sendOffer(const std::string& toPeerId, const std::string& sdp)
{
    SignalingMessage message;
    message.type = MessageType::OFFER;
    message.fromPeerId = m_peerId;
    message.toPeerId = toPeerId;
    message.data = sdp;
    message.timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::system_clock::now().time_since_epoch()).count();

    return sendMessage(message);
}

bool SignalingClient::sendAnswer(const std::string& toPeerId, const std::string& sdp)
{
    SignalingMessage message;
    message.type = MessageType::ANSWER;
    message.fromPeerId = m_peerId;
    message.toPeerId = toPeerId;
    message.data = sdp;
    message.timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::system_clock::now().time_since_epoch()).count();

    return sendMessage(message);
}

bool SignalingClient::sendIceCandidate(const std::string& toPeerId, const std::string& candidate, 
                                     const std::string& sdpMid, int sdpMLineIndex)
{
    json candidateJson;
    candidateJson["candidate"] = candidate;
    candidateJson["sdpMid"] = sdpMid;
    candidateJson["sdpMLineIndex"] = sdpMLineIndex;

    SignalingMessage message;
    message.type = MessageType::ICE_CANDIDATE;
    message.fromPeerId = m_peerId;
    message.toPeerId = toPeerId;
    message.data = candidateJson.dump();
    message.timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::system_clock::now().time_since_epoch()).count();

    return sendMessage(message);
}

bool SignalingClient::sendMessage(const SignalingMessage& message)
{
    if (!isConnected())
    {
        logError("Cannot send message: not connected");
        return false;
    }

    try
    {
        std::string messageJson = serializeMessage(message);
        
        {
            std::lock_guard<std::mutex> lock(m_sendQueueMutex);
            m_sendQueue.push(message);
        }

        logDebug("Queued signaling message: " + messageTypeToString(message.type));
        return true;
    }
    catch (const std::exception& ex)
    {
        logError("Failed to send message: " + std::string(ex.what()));
        return false;
    }
}

// Callback setters
void SignalingClient::setOnConnectionStateChange(OnConnectionStateChangeCallback callback)
{
    m_onConnectionStateChange = callback;
}

void SignalingClient::setOnMessageReceived(OnMessageReceivedCallback callback)
{
    m_onMessageReceived = callback;
}

void SignalingClient::setOnPeerConnected(OnPeerConnectedCallback callback)
{
    m_onPeerConnected = callback;
}

void SignalingClient::setOnPeerDisconnected(OnPeerDisconnectedCallback callback)
{
    m_onPeerDisconnected = callback;
}

void SignalingClient::setOnError(OnErrorCallback callback)
{
    m_onError = callback;
}

std::vector<std::string> SignalingClient::getConnectedPeers() const
{
    std::lock_guard<std::mutex> lock(m_peersListMutex);
    return m_connectedPeers;
}

std::string SignalingClient::getPeerId() const
{
    return m_peerId;
}

// Private methods - placeholder implementations
void SignalingClient::connectionThreadFunction()
{
    logInfo("Connection thread started");

    while (m_running)
    {
        // Placeholder: Handle WebSocket connection maintenance
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }

    logInfo("Connection thread ended");
}

void SignalingClient::messageThreadFunction()
{
    logInfo("Message thread started");

    while (m_running)
    {
        processSendQueue();
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }

    logInfo("Message thread ended");
}

bool SignalingClient::initializeWebSocket()
{
    logInfo("Initializing WebSocket connection");
    
    // Placeholder: Initialize WebSocket connection
    // In real implementation, you would use websocketpp or similar library
    
    // Simulate successful connection
    std::this_thread::sleep_for(std::chrono::milliseconds(500));
    updateConnectionState(ConnectionState::CONNECTED);
    
    return true;
}

void SignalingClient::cleanupWebSocket()
{
    logInfo("Cleaning up WebSocket connection");
    
    // Placeholder: Clean up WebSocket resources
    m_websocketHandle = nullptr;
}

bool SignalingClient::sendRawMessage(const std::string& message)
{
    // Placeholder: Send raw message through WebSocket
    logDebug("Sending raw message: " + message.substr(0, 100) + "...");
    return true;
}

void SignalingClient::handleReceivedMessage(const std::string& message)
{
    try
    {
        auto signalingMessage = deserializeMessage(message);
        
        // Handle different message types
        switch (signalingMessage.type)
        {
            case MessageType::PEER_CONNECTED:
                addConnectedPeer(signalingMessage.fromPeerId);
                if (m_onPeerConnected)
                    m_onPeerConnected(signalingMessage.fromPeerId);
                break;
                
            case MessageType::PEER_DISCONNECTED:
                removeConnectedPeer(signalingMessage.fromPeerId);
                if (m_onPeerDisconnected)
                    m_onPeerDisconnected(signalingMessage.fromPeerId);
                break;
                
            case MessageType::ERROR:
                if (m_onError)
                    m_onError(signalingMessage.data);
                break;
                
            default:
                if (m_onMessageReceived)
                    m_onMessageReceived(signalingMessage);
                break;
        }
    }
    catch (const std::exception& ex)
    {
        logError("Failed to handle received message: " + std::string(ex.what()));
    }
}

void SignalingClient::processSendQueue()
{
    std::unique_lock<std::mutex> lock(m_sendQueueMutex);
    
    while (!m_sendQueue.empty())
    {
        auto message = m_sendQueue.front();
        m_sendQueue.pop();
        
        lock.unlock();
        
        std::string messageJson = serializeMessage(message);
        sendRawMessage(messageJson);
        
        lock.lock();
    }
}

void SignalingClient::updateConnectionState(ConnectionState newState)
{
    if (m_connectionState != newState)
    {
        m_connectionState = newState;
        
        if (m_onConnectionStateChange)
        {
            m_onConnectionStateChange(newState);
        }
    }
}

void SignalingClient::addConnectedPeer(const std::string& peerId)
{
    std::lock_guard<std::mutex> lock(m_peersListMutex);
    
    auto it = std::find(m_connectedPeers.begin(), m_connectedPeers.end(), peerId);
    if (it == m_connectedPeers.end())
    {
        m_connectedPeers.push_back(peerId);
        logInfo("Peer connected: " + peerId);
    }
}

void SignalingClient::removeConnectedPeer(const std::string& peerId)
{
    std::lock_guard<std::mutex> lock(m_peersListMutex);
    
    auto it = std::find(m_connectedPeers.begin(), m_connectedPeers.end(), peerId);
    if (it != m_connectedPeers.end())
    {
        m_connectedPeers.erase(it);
        logInfo("Peer disconnected: " + peerId);
    }
}

// Message serialization/deserialization
std::string SignalingClient::serializeMessage(const SignalingMessage& message)
{
    json j;
    j["type"] = messageTypeToString(message.type);
    j["fromPeerId"] = message.fromPeerId;
    j["toPeerId"] = message.toPeerId;
    j["data"] = message.data;
    j["timestamp"] = message.timestamp;
    
    return j.dump();
}

SignalingClient::SignalingMessage SignalingClient::deserializeMessage(const std::string& jsonStr)
{
    SignalingMessage message;
    
    try
    {
        auto j = json::parse(jsonStr);
        
        message.type = stringToMessageType(j["type"]);
        message.fromPeerId = j.value("fromPeerId", "");
        message.toPeerId = j.value("toPeerId", "");
        message.data = j.value("data", "");
        message.timestamp = j.value("timestamp", 0LL);
    }
    catch (const std::exception& ex)
    {
        logError("Failed to deserialize message: " + std::string(ex.what()));
    }
    
    return message;
}

std::string SignalingClient::messageTypeToString(MessageType type)
{
    switch (type)
    {
        case MessageType::REGISTER: return "register";
        case MessageType::OFFER: return "offer";
        case MessageType::ANSWER: return "answer";
        case MessageType::ICE_CANDIDATE: return "ice_candidate";
        case MessageType::PEER_CONNECTED: return "peer_connected";
        case MessageType::PEER_DISCONNECTED: return "peer_disconnected";
        case MessageType::ERROR: return "error";
        default: return "unknown";
    }
}

SignalingClient::MessageType SignalingClient::stringToMessageType(const std::string& typeStr)
{
    if (typeStr == "register") return MessageType::REGISTER;
    if (typeStr == "offer") return MessageType::OFFER;
    if (typeStr == "answer") return MessageType::ANSWER;
    if (typeStr == "ice_candidate") return MessageType::ICE_CANDIDATE;
    if (typeStr == "peer_connected") return MessageType::PEER_CONNECTED;
    if (typeStr == "peer_disconnected") return MessageType::PEER_DISCONNECTED;
    if (typeStr == "error") return MessageType::ERROR;
    
    return MessageType::ERROR;
}

// Static WebSocket callbacks (placeholder)
void SignalingClient::onWebSocketOpen(void* userData)
{
    auto* client = static_cast<SignalingClient*>(userData);
    client->logInfo("WebSocket connection opened");
    client->updateConnectionState(ConnectionState::CONNECTED);
}

void SignalingClient::onWebSocketClose(void* userData, int code, const char* reason)
{
    auto* client = static_cast<SignalingClient*>(userData);
    client->logInfo("WebSocket connection closed: " + std::string(reason ? reason : ""));
    client->updateConnectionState(ConnectionState::DISCONNECTED);
}

void SignalingClient::onWebSocketMessage(void* userData, const char* data, size_t length)
{
    auto* client = static_cast<SignalingClient*>(userData);
    std::string message(data, length);
    client->handleReceivedMessage(message);
}

void SignalingClient::onWebSocketError(void* userData, const char* error)
{
    auto* client = static_cast<SignalingClient*>(userData);
    client->logError("WebSocket error: " + std::string(error ? error : ""));
    client->updateConnectionState(ConnectionState::FAILED);
    
    if (client->m_onError)
    {
        client->m_onError(error ? error : "WebSocket error");
    }
}

// Logging methods
void SignalingClient::logDebug(const std::string& message)
{
    LOG_DEBUG("[Signaling] " + message);
}

void SignalingClient::logInfo(const std::string& message)
{
    LOG_INFO("[Signaling] " + message);
}

void SignalingClient::logWarning(const std::string& message)
{
    LOG_WARNING("[Signaling] " + message);
}

void SignalingClient::logError(const std::string& message)
{
    LOG_ERROR("[Signaling] " + message);
}
