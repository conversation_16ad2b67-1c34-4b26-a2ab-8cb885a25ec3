using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using SuperBotServer.Services;
using SuperBotServer.Models;
using System.Text;

namespace SuperBotServer.Examples
{
    /// <summary>
    /// Example demonstrating WebRTC functionality in SuperBot
    /// </summary>
    public class WebRTCExample
    {
        private readonly ILogger<WebRTCExample> _logger;
        private readonly IWebRTCService _webRtcService;
        private readonly IServiceProvider _serviceProvider;

        public WebRTCExample(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
            _logger = serviceProvider.GetRequiredService<ILogger<WebRTCExample>>();
            _webRtcService = serviceProvider.GetRequiredService<IWebRTCService>();
        }

        /// <summary>
        /// Demonstrates basic WebRTC setup and peer connection
        /// </summary>
        public async Task RunBasicWebRTCExampleAsync()
        {
            _logger.LogInformation("Starting Basic WebRTC Example");

            try
            {
                // Step 1: Configure WebRTC
                var config = new WebRTCConfiguration
                {
                    SignalingServerUrl = "ws://localhost",
                    SignalingServerPort = 8080,
                    IceServers = new List<string>
                    {
                        "stun:stun.l.google.com:19302",
                        "stun:stun1.l.google.com:19302"
                    },
                    EnableDataChannels = true,
                    EnableLogging = true,
                    LogLevel = "Info",
                    ConnectionTimeout = 30000,
                    HeartbeatInterval = 10000
                };

                // Step 2: Initialize WebRTC service
                _logger.LogInformation("Initializing WebRTC service...");
                var initResult = await _webRtcService.InitializeAsync(config);
                if (!initResult)
                {
                    _logger.LogError("Failed to initialize WebRTC service");
                    return;
                }

                // Step 3: Start signaling server
                _logger.LogInformation("Starting signaling server...");
                var startResult = await _webRtcService.StartSignalingServerAsync();
                if (!startResult)
                {
                    _logger.LogError("Failed to start signaling server");
                    return;
                }

                // Step 4: Setup event handlers
                SetupEventHandlers();

                // Step 5: Create peer connections
                _logger.LogInformation("Creating peer connections...");
                var peerIds = new[] { "client-001", "client-002", "client-003" };
                
                foreach (var peerId in peerIds)
                {
                    var createResult = await _webRtcService.CreatePeerConnectionAsync(peerId);
                    if (createResult)
                    {
                        _logger.LogInformation("Created peer connection for {PeerId}", peerId);
                    }
                    else
                    {
                        _logger.LogError("Failed to create peer connection for {PeerId}", peerId);
                    }
                }

                // Step 6: Demonstrate data transmission
                await DemonstrateDataTransmissionAsync(peerIds[0]);

                // Step 7: Demonstrate screen frame transmission
                await DemonstrateScreenFrameTransmissionAsync(peerIds[1]);

                // Step 8: Demonstrate quality control
                await DemonstrateQualityControlAsync(peerIds[2]);

                // Step 9: Show statistics
                await ShowStatisticsAsync();

                // Wait for user input to continue
                _logger.LogInformation("WebRTC example running. Press any key to stop...");
                Console.ReadKey();

                // Step 10: Cleanup
                await CleanupAsync();

                _logger.LogInformation("Basic WebRTC Example completed successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in Basic WebRTC Example");
            }
        }

        /// <summary>
        /// Demonstrates advanced WebRTC features
        /// </summary>
        public async Task RunAdvancedWebRTCExampleAsync()
        {
            _logger.LogInformation("Starting Advanced WebRTC Example");

            try
            {
                // Initialize with advanced configuration
                var config = new WebRTCConfiguration
                {
                    SignalingServerUrl = "ws://localhost",
                    SignalingServerPort = 8080,
                    IceServers = new List<string>
                    {
                        "stun:stun.l.google.com:19302",
                        "turn:your-turn-server.com:3478" // Add TURN server for NAT traversal
                    },
                    EnableDataChannels = true,
                    EnableLogging = true,
                    LogLevel = "Debug",
                    ConnectionTimeout = 30000,
                    HeartbeatInterval = 5000
                };

                await _webRtcService.InitializeAsync(config);
                await _webRtcService.StartSignalingServerAsync();

                SetupEventHandlers();

                // Demonstrate multiple concurrent connections
                await DemonstrateMultipleConnectionsAsync();

                // Demonstrate bandwidth adaptation
                await DemonstrateBandwidthAdaptationAsync();

                // Demonstrate connection recovery
                await DemonstrateConnectionRecoveryAsync();

                // Demonstrate performance monitoring
                await DemonstratePerformanceMonitoringAsync();

                _logger.LogInformation("Advanced WebRTC Example completed");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in Advanced WebRTC Example");
            }
        }

        private void SetupEventHandlers()
        {
            _webRtcService.PeerConnected += (sender, peerInfo) =>
            {
                _logger.LogInformation("Peer connected: {PeerId} from {RemoteAddress}", 
                    peerInfo.PeerId, peerInfo.RemoteAddress);
            };

            _webRtcService.PeerDisconnected += (sender, peerId) =>
            {
                _logger.LogInformation("Peer disconnected: {PeerId}", peerId);
            };

            _webRtcService.DataReceived += (sender, args) =>
            {
                var message = Encoding.UTF8.GetString(args.data);
                _logger.LogInformation("Data received from {PeerId}: {Message}", args.peerId, message);
            };

            _webRtcService.ScreenFrameReceived += (sender, args) =>
            {
                _logger.LogInformation("Screen frame received from {PeerId}: {Width}x{Height}", 
                    args.peerId, args.frame.Width, args.frame.Height);
            };

            _webRtcService.PeerError += (sender, args) =>
            {
                _logger.LogError("Peer error for {PeerId}: {Error}", args.peerId, args.error);
            };
        }

        private async Task DemonstrateDataTransmissionAsync(string peerId)
        {
            _logger.LogInformation("Demonstrating data transmission to {PeerId}", peerId);

            var messages = new[]
            {
                "Hello WebRTC!",
                "This is a test message",
                "WebRTC data channels are working!",
                "Goodbye!"
            };

            foreach (var message in messages)
            {
                var data = Encoding.UTF8.GetBytes(message);
                var result = await _webRtcService.SendDataAsync(peerId, data);
                
                if (result)
                {
                    _logger.LogInformation("Sent message to {PeerId}: {Message}", peerId, message);
                }
                else
                {
                    _logger.LogError("Failed to send message to {PeerId}: {Message}", peerId, message);
                }

                await Task.Delay(1000); // Wait between messages
            }
        }

        private async Task DemonstrateScreenFrameTransmissionAsync(string peerId)
        {
            _logger.LogInformation("Demonstrating screen frame transmission to {PeerId}", peerId);

            // Simulate screen frames
            for (int i = 0; i < 5; i++)
            {
                var frame = new ScreenFrame
                {
                    Width = 1920,
                    Height = 1080,
                    Format = ScreenFormat.RGB24,
                    Data = new byte[1920 * 1080 * 3], // Placeholder data
                    Timestamp = DateTime.UtcNow,
                    FrameId = (uint)i,
                    IsKeyFrame = i == 0 // First frame is key frame
                };

                // Fill with some test pattern
                for (int j = 0; j < frame.Data.Length; j += 3)
                {
                    frame.Data[j] = (byte)(i * 50); // Red
                    frame.Data[j + 1] = (byte)(j % 256); // Green
                    frame.Data[j + 2] = (byte)((i + j) % 256); // Blue
                }

                var result = await _webRtcService.SendScreenFrameAsync(peerId, frame);
                
                if (result)
                {
                    _logger.LogInformation("Sent screen frame {FrameId} to {PeerId}", frame.FrameId, peerId);
                }
                else
                {
                    _logger.LogError("Failed to send screen frame {FrameId} to {PeerId}", frame.FrameId, peerId);
                }

                await Task.Delay(100); // 10 FPS
            }
        }

        private async Task DemonstrateQualityControlAsync(string peerId)
        {
            _logger.LogInformation("Demonstrating quality control for {PeerId}", peerId);

            var qualityLevels = new[]
            {
                new QualityControlMessage { ResizePercentage = 100, CompressionLevel = 1, FrameRate = 60 },
                new QualityControlMessage { ResizePercentage = 75, CompressionLevel = 3, FrameRate = 30 },
                new QualityControlMessage { ResizePercentage = 50, CompressionLevel = 5, FrameRate = 15 },
                new QualityControlMessage { ResizePercentage = 25, CompressionLevel = 7, FrameRate = 10 }
            };

            foreach (var quality in qualityLevels)
            {
                var result = await _webRtcService.SendQualityControlAsync(peerId, quality);
                
                if (result)
                {
                    _logger.LogInformation("Sent quality control to {PeerId}: {Resize}% resize, compression {Compression}, {FrameRate} FPS", 
                        peerId, quality.ResizePercentage, quality.CompressionLevel, quality.FrameRate);
                }

                await Task.Delay(2000); // Wait between quality changes
            }

            // Request quality status
            await _webRtcService.RequestQualityStatusAsync(peerId);
        }

        private async Task ShowStatisticsAsync()
        {
            _logger.LogInformation("Showing WebRTC statistics");

            var allStats = await _webRtcService.GetAllStatisticsAsync();
            
            foreach (var kvp in allStats)
            {
                var peerId = kvp.Key;
                var stats = kvp.Value;
                
                _logger.LogInformation("Statistics for {PeerId}:", peerId);
                _logger.LogInformation("  Bytes Sent: {BytesSent}", stats.BytesSent);
                _logger.LogInformation("  Bytes Received: {BytesReceived}", stats.BytesReceived);
                _logger.LogInformation("  Messages Sent: {MessagesSent}", stats.MessagesSent);
                _logger.LogInformation("  Messages Received: {MessagesReceived}", stats.MessagesReceived);
                _logger.LogInformation("  Round Trip Time: {RTT}ms", stats.RoundTripTime);
                _logger.LogInformation("  Bandwidth: {Bandwidth} bps", stats.Bandwidth);
            }
        }

        private async Task DemonstrateMultipleConnectionsAsync()
        {
            _logger.LogInformation("Demonstrating multiple concurrent connections");

            var peerCount = 10;
            var tasks = new List<Task>();

            for (int i = 0; i < peerCount; i++)
            {
                var peerId = $"concurrent-peer-{i:D3}";
                tasks.Add(_webRtcService.CreatePeerConnectionAsync(peerId));
            }

            await Task.WhenAll(tasks);
            
            var connectedPeers = await _webRtcService.GetConnectedPeersAsync();
            _logger.LogInformation("Successfully created {Count} concurrent connections", connectedPeers.Count);
        }

        private async Task DemonstrateBandwidthAdaptationAsync()
        {
            _logger.LogInformation("Demonstrating bandwidth adaptation");
            
            // This would involve monitoring network conditions and adjusting quality
            // For now, just demonstrate the concept
            await Task.Delay(1000);
            _logger.LogInformation("Bandwidth adaptation demonstration completed");
        }

        private async Task DemonstrateConnectionRecoveryAsync()
        {
            _logger.LogInformation("Demonstrating connection recovery");
            
            // This would involve simulating network failures and recovery
            await Task.Delay(1000);
            _logger.LogInformation("Connection recovery demonstration completed");
        }

        private async Task DemonstratePerformanceMonitoringAsync()
        {
            _logger.LogInformation("Demonstrating performance monitoring");
            
            // Monitor performance metrics over time
            for (int i = 0; i < 5; i++)
            {
                var allStats = await _webRtcService.GetAllStatisticsAsync();
                _logger.LogInformation("Performance snapshot {Snapshot}: {PeerCount} active peers", 
                    i + 1, allStats.Count);
                
                await Task.Delay(2000);
            }
        }

        private async Task CleanupAsync()
        {
            _logger.LogInformation("Cleaning up WebRTC resources");

            var connectedPeers = await _webRtcService.GetConnectedPeersAsync();
            
            foreach (var peer in connectedPeers)
            {
                await _webRtcService.ClosePeerConnectionAsync(peer.PeerId);
            }

            await _webRtcService.StopSignalingServerAsync();
            await _webRtcService.ShutdownAsync();
        }

        /// <summary>
        /// Entry point for running WebRTC examples
        /// </summary>
        public static async Task Main(string[] args)
        {
            // Setup dependency injection
            var services = new ServiceCollection();
            
            services.AddLogging(builder =>
            {
                builder.AddConsole();
                builder.SetMinimumLevel(LogLevel.Information);
            });

            // Add configuration
            var configuration = new ConfigurationBuilder()
                .AddJsonFile("appsettings.json", optional: true)
                .AddEnvironmentVariables()
                .Build();
            
            services.AddSingleton<IConfiguration>(configuration);
            services.AddTransient<IWebRTCService, WebRTCService>();
            services.AddTransient<ICompressionService, CompressionService>();
            services.AddTransient<ISecurityService, SecurityService>();
            services.AddTransient<WebRTCExample>();

            var serviceProvider = services.BuildServiceProvider();
            var example = serviceProvider.GetRequiredService<WebRTCExample>();

            Console.WriteLine("SuperBot WebRTC Example");
            Console.WriteLine("1. Basic WebRTC Example");
            Console.WriteLine("2. Advanced WebRTC Example");
            Console.Write("Select example (1 or 2): ");

            var choice = Console.ReadLine();
            
            switch (choice)
            {
                case "1":
                    await example.RunBasicWebRTCExampleAsync();
                    break;
                case "2":
                    await example.RunAdvancedWebRTCExampleAsync();
                    break;
                default:
                    Console.WriteLine("Invalid choice. Running basic example...");
                    await example.RunBasicWebRTCExampleAsync();
                    break;
            }

            Console.WriteLine("Example completed. Press any key to exit...");
            Console.ReadKey();
        }
    }
}
