{"format": 1, "restore": {"D:\\Project\\SuperBot\\Server\\SuperBotServer\\SuperBotServer.csproj": {}}, "projects": {"D:\\Project\\SuperBot\\Server\\SuperBotServer\\SuperBotServer.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Project\\SuperBot\\Server\\SuperBotServer\\SuperBotServer.csproj", "projectName": "SuperBotServer", "projectPath": "D:\\Project\\SuperBot\\Server\\SuperBotServer\\SuperBotServer.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project\\SuperBot\\Server\\SuperBotServer\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"CommunityToolkit.Mvvm": {"target": "Package", "version": "[8.2.2, )"}, "K4os.Compression.LZ4": {"target": "Package", "version": "[1.3.8, )"}, "LZ4": {"target": "Package", "version": "[1.0.15.93, )"}, "MaterialDesignColors": {"target": "Package", "version": "[2.1.4, )"}, "MaterialDesignThemes": {"target": "Package", "version": "[4.9.0, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Configuration.EnvironmentVariables": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Logging.Console": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Logging.Debug": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Options": {"target": "Package", "version": "[8.0.2, )"}, "Microsoft.Extensions.Options.ConfigurationExtensions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Xaml.Behaviors.Wpf": {"target": "Package", "version": "[1.1.77, )"}, "ModernWpfUI": {"target": "Package", "version": "[0.9.6, )"}, "System.Drawing.Common": {"target": "Package", "version": "[8.0.0, )"}, "System.Security.Cryptography.Algorithms": {"target": "Package", "version": "[4.3.1, )"}, "System.Text.Json": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.202/PortableRuntimeIdentifierGraph.json"}}}}}