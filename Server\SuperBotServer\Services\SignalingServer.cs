using Microsoft.Extensions.Logging;
using System.Net;
using System.Net.WebSockets;
using System.Text;
using System.Text.Json;
using System.Collections.Concurrent;

namespace SuperBotServer.Services
{
    // Signaling message types
    public enum SignalingMessageType
    {
        Register,
        Offer,
        Answer,
        IceCandidate,
        PeerConnected,
        PeerDisconnected,
        Error,
        Heartbeat
    }

    // Signaling message structure
    public class SignalingMessage
    {
        public SignalingMessageType Type { get; set; }
        public string FromPeerId { get; set; } = string.Empty;
        public string ToPeerId { get; set; } = string.Empty;
        public string Data { get; set; } = string.Empty;
        public long Timestamp { get; set; }
        public Dictionary<string, object>? Metadata { get; set; }
    }

    // Connected peer information
    public class ConnectedPeer
    {
        public string PeerId { get; set; } = string.Empty;
        public WebSocket WebSocket { get; set; } = null!;
        public string RemoteEndpoint { get; set; } = string.Empty;
        public DateTime ConnectedTime { get; set; }
        public DateTime LastActivity { get; set; }
        public bool IsAlive { get; set; } = true;
    }

    internal class SignalingServer : ISignalingServer
    {
        private readonly ILogger _logger;
        private readonly WebRTCConfiguration _configuration;

        private HttpListener? _httpListener;
        private CancellationTokenSource? _cancellationTokenSource;
        private Task? _serverTask;
        private readonly ConcurrentDictionary<string, ConnectedPeer> _connectedPeers = new();
        private Timer? _heartbeatTimer;

        private bool _isRunning;
        private bool _disposed;

        public bool IsRunning => _isRunning;

        public event EventHandler<string>? PeerConnected;
        public event EventHandler<string>? PeerDisconnected;
        public event EventHandler<(string peerId, string message)>? MessageReceived;
        public event EventHandler<string>? Error;

        public SignalingServer(ILogger logger, WebRTCConfiguration configuration)
        {
            _logger = logger;
            _configuration = configuration;
        }

        public async Task<bool> StartAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                if (_isRunning)
                {
                    _logger.LogWarning("Signaling server is already running");
                    return true;
                }

                _logger.LogInformation("Starting signaling server on {Url}:{Port}",
                    _configuration.SignalingServerUrl, _configuration.SignalingServerPort);

                // Initialize HTTP listener for WebSocket connections
                _httpListener = new HttpListener();
                var prefix = $"http://+:{_configuration.SignalingServerPort}/";
                _httpListener.Prefixes.Add(prefix);

                _httpListener.Start();
                _logger.LogInformation("HTTP listener started on {Prefix}", prefix);

                // Start cancellation token source
                _cancellationTokenSource = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);

                // Start server task
                _serverTask = Task.Run(() => ServerLoop(_cancellationTokenSource.Token), _cancellationTokenSource.Token);

                // Start heartbeat timer
                _heartbeatTimer = new Timer(SendHeartbeats, null,
                    TimeSpan.FromSeconds(30), TimeSpan.FromSeconds(30));

                _isRunning = true;
                _logger.LogInformation("Signaling server started successfully");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to start signaling server");
                await StopAsync();
                return false;
            }
        }

        public async Task StopAsync()
        {
            if (!_isRunning) return;

            try
            {
                _logger.LogInformation("Stopping signaling server");

                // Stop heartbeat timer
                _heartbeatTimer?.Dispose();
                _heartbeatTimer = null;

                // Cancel operations
                _cancellationTokenSource?.Cancel();

                // Close all peer connections
                await DisconnectAllPeersAsync();

                // Stop HTTP listener
                _httpListener?.Stop();
                _httpListener?.Close();

                // Wait for server task to complete
                if (_serverTask != null)
                {
                    await _serverTask.WaitAsync(TimeSpan.FromSeconds(5));
                }

                _isRunning = false;
                _logger.LogInformation("Signaling server stopped successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error stopping signaling server");
            }
            finally
            {
                _cancellationTokenSource?.Dispose();
                _cancellationTokenSource = null;
                _serverTask = null;
                _httpListener = null;
            }
        }

        private async Task ServerLoop(CancellationToken cancellationToken)
        {
            _logger.LogInformation("Signaling server loop started");

            try
            {
                while (!cancellationToken.IsCancellationRequested && _httpListener != null)
                {
                    try
                    {
                        var context = await _httpListener.GetContextAsync();

                        // Handle WebSocket upgrade request
                        if (context.Request.IsWebSocketRequest)
                        {
                            _ = Task.Run(() => HandleWebSocketConnection(context), cancellationToken);
                        }
                        else
                        {
                            // Return 400 for non-WebSocket requests
                            context.Response.StatusCode = 400;
                            context.Response.Close();
                        }
                    }
                    catch (ObjectDisposedException)
                    {
                        // Expected when stopping the server
                        break;
                    }
                    catch (HttpListenerException ex) when (ex.ErrorCode == 995)
                    {
                        // Expected when stopping the server
                        break;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error in server loop");
                        Error?.Invoke(this, ex.Message);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Fatal error in server loop");
                Error?.Invoke(this, ex.Message);
            }

            _logger.LogInformation("Signaling server loop ended");
        }

        private async Task HandleWebSocketConnection(HttpListenerContext context)
        {
            WebSocket? webSocket = null;
            string peerId = string.Empty;

            try
            {
                // Accept WebSocket connection
                var webSocketContext = await context.AcceptWebSocketAsync(null);
                webSocket = webSocketContext.WebSocket;

                var remoteEndpoint = context.Request.RemoteEndPoint?.ToString() ?? "unknown";
                _logger.LogInformation("WebSocket connection established from {RemoteEndpoint}", remoteEndpoint);

                // Wait for registration message
                var registrationResult = await WaitForRegistration(webSocket);
                if (!registrationResult.success)
                {
                    _logger.LogWarning("Failed to register peer from {RemoteEndpoint}", remoteEndpoint);
                    await webSocket.CloseAsync(WebSocketCloseStatus.PolicyViolation,
                        "Registration required", CancellationToken.None);
                    return;
                }

                peerId = registrationResult.peerId;
                _logger.LogInformation("Peer {PeerId} registered from {RemoteEndpoint}", peerId, remoteEndpoint);

                // Add to connected peers
                var peer = new ConnectedPeer
                {
                    PeerId = peerId,
                    WebSocket = webSocket,
                    RemoteEndpoint = remoteEndpoint,
                    ConnectedTime = DateTime.UtcNow,
                    LastActivity = DateTime.UtcNow
                };

                _connectedPeers.TryAdd(peerId, peer);
                PeerConnected?.Invoke(this, peerId);

                // Send peer list to new peer
                await SendPeerListAsync(peerId);

                // Notify other peers about new connection
                await BroadcastPeerConnectedAsync(peerId);

                // Handle messages from this peer
                await HandlePeerMessages(peer);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error handling WebSocket connection for peer {PeerId}", peerId);
            }
            finally
            {
                // Clean up peer connection
                if (!string.IsNullOrEmpty(peerId))
                {
                    await DisconnectPeerAsync(peerId);
                }
                else if (webSocket != null)
                {
                    try
                    {
                        await webSocket.CloseAsync(WebSocketCloseStatus.InternalServerError,
                            "Connection error", CancellationToken.None);
                    }
                    catch { }
                }
            }
        }

        private async Task<(bool success, string peerId)> WaitForRegistration(WebSocket webSocket)
        {
            try
            {
                var buffer = new byte[4096];
                var result = await webSocket.ReceiveAsync(new ArraySegment<byte>(buffer),
                    CancellationToken.None);

                if (result.MessageType == WebSocketMessageType.Text)
                {
                    var messageText = Encoding.UTF8.GetString(buffer, 0, result.Count);
                    var message = JsonSerializer.Deserialize<SignalingMessage>(messageText);

                    if (message?.Type == SignalingMessageType.Register &&
                        !string.IsNullOrEmpty(message.FromPeerId))
                    {
                        return (true, message.FromPeerId);
                    }
                }

                return (false, string.Empty);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error waiting for registration");
                return (false, string.Empty);
            }
        }

        private async Task HandlePeerMessages(ConnectedPeer peer)
        {
            var buffer = new byte[8192];

            try
            {
                while (peer.WebSocket.State == WebSocketState.Open && peer.IsAlive)
                {
                    var result = await peer.WebSocket.ReceiveAsync(new ArraySegment<byte>(buffer),
                        CancellationToken.None);

                    peer.LastActivity = DateTime.UtcNow;

                    if (result.MessageType == WebSocketMessageType.Text)
                    {
                        var messageText = Encoding.UTF8.GetString(buffer, 0, result.Count);
                        _logger.LogDebug("Received message from {PeerId}: {Message}", peer.PeerId, messageText);

                        await ProcessSignalingMessage(peer.PeerId, messageText);
                    }
                    else if (result.MessageType == WebSocketMessageType.Close)
                    {
                        _logger.LogInformation("Peer {PeerId} closed connection", peer.PeerId);
                        break;
                    }
                }
            }
            catch (WebSocketException ex)
            {
                _logger.LogWarning(ex, "WebSocket error for peer {PeerId}", peer.PeerId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error handling messages for peer {PeerId}", peer.PeerId);
            }
        }

        private async Task ProcessSignalingMessage(string fromPeerId, string messageText)
        {
            try
            {
                var message = JsonSerializer.Deserialize<SignalingMessage>(messageText);
                if (message == null) return;

                message.FromPeerId = fromPeerId; // Ensure correct sender
                message.Timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();

                // Route message to target peer or broadcast
                if (!string.IsNullOrEmpty(message.ToPeerId))
                {
                    await ForwardMessageToPeerAsync(message);
                }
                else
                {
                    // Broadcast or handle server-side message
                    MessageReceived?.Invoke(this, (fromPeerId, messageText));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing signaling message from {PeerId}", fromPeerId);
            }
        }

        private async Task ForwardMessageToPeerAsync(SignalingMessage message)
        {
            if (_connectedPeers.TryGetValue(message.ToPeerId, out var targetPeer))
            {
                try
                {
                    var messageJson = JsonSerializer.Serialize(message);
                    var messageBytes = Encoding.UTF8.GetBytes(messageJson);

                    await targetPeer.WebSocket.SendAsync(new ArraySegment<byte>(messageBytes),
                        WebSocketMessageType.Text, true, CancellationToken.None);

                    _logger.LogDebug("Forwarded message from {FromPeer} to {ToPeer}",
                        message.FromPeerId, message.ToPeerId);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error forwarding message to peer {PeerId}", message.ToPeerId);
                }
            }
            else
            {
                _logger.LogWarning("Target peer {PeerId} not found for message from {FromPeer}",
                    message.ToPeerId, message.FromPeerId);
            }
        }

        private async Task SendPeerListAsync(string peerId)
        {
            try
            {
                if (!_connectedPeers.TryGetValue(peerId, out var peer))
                    return;

                var peerList = _connectedPeers.Keys.Where(id => id != peerId).ToList();

                var message = new SignalingMessage
                {
                    Type = SignalingMessageType.PeerConnected,
                    FromPeerId = "server",
                    ToPeerId = peerId,
                    Data = JsonSerializer.Serialize(peerList),
                    Timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()
                };

                var messageJson = JsonSerializer.Serialize(message);
                var messageBytes = Encoding.UTF8.GetBytes(messageJson);

                await peer.WebSocket.SendAsync(new ArraySegment<byte>(messageBytes),
                    WebSocketMessageType.Text, true, CancellationToken.None);

                _logger.LogDebug("Sent peer list to {PeerId}: {PeerCount} peers", peerId, peerList.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending peer list to {PeerId}", peerId);
            }
        }

        private async Task BroadcastPeerConnectedAsync(string peerId)
        {
            try
            {
                var message = new SignalingMessage
                {
                    Type = SignalingMessageType.PeerConnected,
                    FromPeerId = "server",
                    ToPeerId = "",
                    Data = peerId,
                    Timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()
                };

                await BroadcastMessageAsync(message, peerId);
                _logger.LogDebug("Broadcasted peer connected: {PeerId}", peerId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error broadcasting peer connected: {PeerId}", peerId);
            }
        }

        private async Task BroadcastPeerDisconnectedAsync(string peerId)
        {
            try
            {
                var message = new SignalingMessage
                {
                    Type = SignalingMessageType.PeerDisconnected,
                    FromPeerId = "server",
                    ToPeerId = "",
                    Data = peerId,
                    Timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()
                };

                await BroadcastMessageAsync(message, peerId);
                _logger.LogDebug("Broadcasted peer disconnected: {PeerId}", peerId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error broadcasting peer disconnected: {PeerId}", peerId);
            }
        }

        private async Task BroadcastMessageAsync(SignalingMessage message, string excludePeerId = "")
        {
            var messageJson = JsonSerializer.Serialize(message);
            var messageBytes = Encoding.UTF8.GetBytes(messageJson);
            var tasks = new List<Task>();

            foreach (var kvp in _connectedPeers)
            {
                if (kvp.Key == excludePeerId) continue;

                var peer = kvp.Value;
                if (peer.WebSocket.State == WebSocketState.Open)
                {
                    tasks.Add(SendMessageToPeerAsync(peer, messageBytes));
                }
            }

            await Task.WhenAll(tasks);
        }

        private async Task SendMessageToPeerAsync(ConnectedPeer peer, byte[] messageBytes)
        {
            try
            {
                await peer.WebSocket.SendAsync(new ArraySegment<byte>(messageBytes),
                    WebSocketMessageType.Text, true, CancellationToken.None);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to send message to peer {PeerId}", peer.PeerId);
                // Mark peer for disconnection
                peer.IsAlive = false;
            }
        }

        private async Task DisconnectPeerAsync(string peerId)
        {
            try
            {
                if (_connectedPeers.TryRemove(peerId, out var peer))
                {
                    _logger.LogInformation("Disconnecting peer {PeerId}", peerId);

                    try
                    {
                        if (peer.WebSocket.State == WebSocketState.Open)
                        {
                            await peer.WebSocket.CloseAsync(WebSocketCloseStatus.NormalClosure,
                                "Peer disconnected", CancellationToken.None);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Error closing WebSocket for peer {PeerId}", peerId);
                    }

                    // Notify other peers
                    await BroadcastPeerDisconnectedAsync(peerId);

                    // Raise event
                    PeerDisconnected?.Invoke(this, peerId);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error disconnecting peer {PeerId}", peerId);
            }
        }

        private async Task DisconnectAllPeersAsync()
        {
            try
            {
                _logger.LogInformation("Disconnecting all peers");

                var disconnectTasks = new List<Task>();
                var peerIds = _connectedPeers.Keys.ToList();

                foreach (var peerId in peerIds)
                {
                    disconnectTasks.Add(DisconnectPeerAsync(peerId));
                }

                await Task.WhenAll(disconnectTasks);
                _connectedPeers.Clear();

                _logger.LogInformation("All peers disconnected");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error disconnecting all peers");
            }
        }

        private void SendHeartbeats(object? state)
        {
            try
            {
                var heartbeatMessage = new SignalingMessage
                {
                    Type = SignalingMessageType.Heartbeat,
                    FromPeerId = "server",
                    ToPeerId = "",
                    Data = "ping",
                    Timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()
                };

                _ = Task.Run(async () =>
                {
                    await BroadcastMessageAsync(heartbeatMessage);
                    await CheckPeerAliveness();
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending heartbeats");
            }
        }

        private async Task CheckPeerAliveness()
        {
            try
            {
                var currentTime = DateTime.UtcNow;
                var timeoutThreshold = TimeSpan.FromSeconds(60); // 60 seconds timeout
                var peersToDisconnect = new List<string>();

                foreach (var kvp in _connectedPeers)
                {
                    var peer = kvp.Value;

                    if (!peer.IsAlive ||
                        peer.WebSocket.State != WebSocketState.Open ||
                        currentTime - peer.LastActivity > timeoutThreshold)
                    {
                        peersToDisconnect.Add(kvp.Key);
                    }
                }

                foreach (var peerId in peersToDisconnect)
                {
                    _logger.LogWarning("Peer {PeerId} appears to be inactive, disconnecting", peerId);
                    await DisconnectPeerAsync(peerId);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking peer aliveness");
            }
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                StopAsync().Wait();
                _disposed = true;
            }
        }
    }
}
