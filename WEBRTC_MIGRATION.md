# WebRTC Migration Guide for SuperBot

## Overview

This document describes the migration of SuperBot from TCP-based networking to WebRTC for improved peer-to-peer communication and better performance.

## Architecture Changes

### Before (TCP-based)
```
Client (C++) <--TCP--> Server (C#)
     |                      |
 WinAPI Socket         TcpClient/SslStream
     |                      |
 Custom Protocol       Custom Protocol
```

### After (WebRTC-based)
```
Client (C++) <--WebRTC--> Server (C#)
     |                        |
 WebRTCClient            IWebRTCService
     |                        |
 Data Channels           Data Channels
     |                        |
 Signaling Server <----------->
```

## Key Components

### Client Side (C++)

1. **WebRTCClient** (`Client/include/WebRTCClient.h`)
   - Main WebRTC client implementation
   - Handles peer connections, data channels
   - Manages ICE candidates and SDP exchange

2. **SignalingClient** (`Client/include/SignalingClient.h`)
   - WebSocket-based signaling for WebRTC handshake
   - Handles offer/answer exchange
   - Manages peer discovery

3. **Dependencies Added**
   - `nlohmann-json`: JSON serialization
   - `websocketpp`: WebSocket client
   - `openssl`: SSL/TLS support
   - `libwebrtc`: WebRTC implementation (to be added)

### Server Side (C#)

1. **IWebRTCService** (`Server/SuperBotServer/Services/IWebRTCService.cs`)
   - WebRTC service interface
   - Manages multiple peer connections
   - Handles signaling and data channels

2. **SignalingServer** (`Server/SuperBotServer/Services/SignalingServer.cs`)
   - WebSocket-based signaling server
   - Routes messages between peers
   - Manages peer registration and discovery

3. **NetworkServiceAdapter** (`Server/SuperBotServer/Services/NetworkServiceAdapter.cs`)
   - Unified interface for both TCP and WebRTC
   - Automatic protocol selection
   - Fallback mechanism

4. **Dependencies Added**
   - `Microsoft.AspNetCore.SignalR.Client`: SignalR support
   - `System.Net.WebSockets.Client`: WebSocket client
   - `Newtonsoft.Json`: JSON serialization
   - `Microsoft.Extensions.Hosting`: Hosting services

## Configuration

### Client Configuration (`Client/webrtc_config.json`)
```json
{
  "webrtc": {
    "enabled": true,
    "signaling": {
      "server_url": "ws://localhost",
      "server_port": 8080
    },
    "ice_servers": [
      {"urls": "stun:stun.l.google.com:19302"}
    ]
  }
}
```

### Server Configuration (`Server/SuperBotServer/appsettings.json`)
```json
{
  "SuperBot": {
    "Network": {
      "Protocol": "WebRTC"
    },
    "WebRTC": {
      "Enabled": true,
      "SignalingServerPort": 8080,
      "IceServers": ["stun:stun.l.google.com:19302"]
    }
  }
}
```

## Migration Steps

### Phase 1: Infrastructure Setup ✅
- [x] Add WebRTC dependencies to vcpkg.json
- [x] Add WebRTC NuGet packages to .csproj
- [x] Create WebRTC interfaces and base classes
- [x] Update CMakeLists.txt with new dependencies
- [x] Create configuration files

### Phase 2: Signaling Implementation (In Progress)
- [ ] Implement SignalingServer in C#
- [ ] Implement SignalingClient in C++
- [ ] Add WebSocket communication
- [ ] Test signaling handshake

### Phase 3: WebRTC Core Implementation
- [ ] Integrate libwebrtc in C++ client
- [ ] Implement peer connection management
- [ ] Add data channel support
- [ ] Implement ICE candidate handling

### Phase 4: Protocol Adaptation
- [ ] Adapt existing message protocol for WebRTC
- [ ] Implement screen frame transmission over data channels
- [ ] Add quality control over WebRTC
- [ ] Implement compression for WebRTC data

### Phase 5: Integration and Testing
- [ ] Integrate WebRTC with existing UI
- [ ] Add protocol selection logic
- [ ] Implement fallback mechanisms
- [ ] Performance testing and optimization

### Phase 6: Advanced Features
- [ ] Add NAT traversal improvements
- [ ] Implement adaptive bitrate
- [ ] Add connection quality monitoring
- [ ] Optimize for low latency

## Benefits of WebRTC Migration

1. **Better NAT Traversal**
   - Built-in STUN/TURN support
   - Automatic hole punching
   - Works behind firewalls

2. **Improved Performance**
   - Direct peer-to-peer communication
   - Lower latency
   - Better bandwidth utilization

3. **Enhanced Security**
   - Built-in DTLS encryption
   - Secure key exchange
   - Protection against eavesdropping

4. **Reliability**
   - Automatic connection recovery
   - Multiple transport options
   - Adaptive quality control

## Current Status

### Completed ✅
- Basic project structure for WebRTC
- Dependencies configuration
- Interface definitions
- Configuration files
- Build system updates

### In Progress 🔄
- Signaling server implementation
- WebRTC client basic structure
- Protocol adaptation layer

### Pending ⏳
- Full WebRTC integration
- libwebrtc dependency
- Complete testing framework
- Performance optimization

## Building with WebRTC Support

### Client (C++)
```bash
cd Client
# Install dependencies
vcpkg install nlohmann-json websocketpp openssl

# Build
mkdir build && cd build
cmake .. -DCMAKE_TOOLCHAIN_FILE=../vcpkg/scripts/buildsystems/vcpkg.cmake
cmake --build .
```

### Server (C#)
```bash
cd Server
dotnet restore
dotnet build
```

## Testing

### Unit Tests
- WebRTC connection establishment
- Signaling message exchange
- Data channel communication
- Protocol fallback mechanisms

### Integration Tests
- End-to-end screen sharing
- Quality control functionality
- Multi-peer scenarios
- Network failure recovery

## Troubleshooting

### Common Issues
1. **Signaling Connection Failed**
   - Check signaling server is running
   - Verify WebSocket URL and port
   - Check firewall settings

2. **ICE Connection Failed**
   - Verify STUN server accessibility
   - Check NAT/firewall configuration
   - Consider TURN server for restrictive networks

3. **Data Channel Not Opening**
   - Verify peer connection state
   - Check data channel configuration
   - Monitor WebRTC logs

### Debug Logging
Enable detailed logging in configuration:
```json
{
  "webrtc": {
    "logging": {
      "enabled": true,
      "level": "DEBUG"
    }
  }
}
```

## Future Enhancements

1. **Multi-stream Support**
   - Multiple data channels
   - Prioritized data transmission
   - Stream multiplexing

2. **Advanced NAT Traversal**
   - Custom TURN server
   - ICE-TCP support
   - Relay optimization

3. **Performance Monitoring**
   - Real-time statistics
   - Connection quality metrics
   - Automatic optimization

4. **Security Enhancements**
   - Custom encryption layers
   - Certificate pinning
   - Advanced authentication

## References

- [WebRTC Specification](https://webrtc.org/)
- [libwebrtc Documentation](https://webrtc.googlesource.com/src/)
- [WebRTC Samples](https://webrtc.github.io/samples/)
- [STUN/TURN Servers](https://gist.github.com/mondain/b0ec1cf5f60ae726202e)
