{
  "Logging": {
    "LogLevel": {
      "Default": "Debug",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information",
      "SuperBotServer.Services.NetworkService": "Debug"
    },
    "Console": {
      "IncludeScopes": false,
      "TimestampFormat": "yyyy-MM-dd HH:mm:ss "
    }
  },
  "SuperBot": {
    "Network": {
      "DefaultPort": 7878,
      "ConnectionTimeout": 10000,
      "HeartbeatInterval": 30000,
      "MaxConnections": 10,
      "CompressionEnabled": true,
      "EncryptionEnabled": true,
      "Protocol": "TCP" // TCP or WebRTC
    },
    "WebRTC": {
      "Enabled": true,
      "SignalingServerUrl": "ws://localhost",
      "SignalingServerPort": 8080,
      "IceServers": [
        "stun:stun.l.google.com:19302",
        "stun:stun1.l.google.com:19302",
        "stun:stun2.l.google.com:19302"
      ],
      "EnableDataChannels": true,
      "ConnectionTimeout": 30000,
      "HeartbeatInterval": 10000,
      "LogLevel": "Info",
      "EnableLogging": true,
      "MaxPeers": 50,
      "EnableFallbackToTCP": true
    },
    "Display": {
      "MaxFrameRate": 60,
      "DefaultQuality": 80,
      "AdaptiveQuality": true,
      "HardwareAcceleration": true
    },
    "Input": {
      "InputDelay": 1,
      "MouseAcceleration": true,
      "KeyRepeatEnabled": true,
      "KeyRepeatDelay": 500,
      "KeyRepeatRate": 30
    },
    "Security": {
      "CertificatePath": "",
      "PrivateKeyPath": "",
      "AllowSelfSignedCertificates": true,
      "RequireClientCertificate": false
    },
    "FileTransfer": {
      "Enabled": true,
      "MaxFileSize": 1073741824,
      "ChunkSize": 65536,
      "AllowedExtensions": [
        ".txt", ".doc", ".docx", ".pdf", ".jpg", ".png", ".gif", ".zip", ".rar"
      ]
    },
    "Clipboard": {
      "Enabled": true,
      "SyncInterval": 1000,
      "MaxTextSize": 1048576,
      "MaxImageSize": 10485760
    },
    "UI": {
      "Theme": "Light",
      "Language": "en-US",
      "AutoConnect": false,
      "MinimizeToTray": true,
      "ShowNotifications": true
    }
  }
}
