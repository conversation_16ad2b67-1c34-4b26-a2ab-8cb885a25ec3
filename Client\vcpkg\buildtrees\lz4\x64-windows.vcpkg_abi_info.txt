cmake 4.0.2
features core
portfile.cmake b70fd07b84540a452f252350e5877063cb6ff924de4a817bd824aab6ff2216cb
ports.cmake a9f8f745f9f4d225039f9810e79670f10f38c7455222865616ce430a06f75d1f
post_build_checks 2
powershell 7.2.24
target-lz4-lz4.diff 82e8dcd050d3249f6ce0598a18136ee5939667a28e41ff5dd220d71b42a156f0
triplet x64-windows
triplet_abi 4556164a2cd3dd6f4742101eabb46def7e71b6e5856faa88e5d005aac12a803c-1d3d767ab10b488977977b83f6c2da7e06afd80b1e375a2a41c60c343e7b3f94-587828565ac2e9afff4fa4d7c3600946b389739d
usage 32490d4c5d482fcef4f551b56ac5db1c6e38e7c5779ff8f656c9d93cdb155470
vcpkg-cmake fc6a38a11a3ddadf85aad062f18de9b56ecef5b42a5349d550b26e8ceb2d2978
vcpkg-cmake-config 389e1a597b13e84440546a2805e489eafb2fab2651776b6611467f24a00f848f
vcpkg.json dbc58f209d5e2ddbfd04ef4984c657a6c097710461114313a26617d983694666
vcpkg_check_features 943b217e0968d64cf2cb9c272608e6a0b497377e792034f819809a79e1502c2b
vcpkg_copy_pdbs d15c2f2822d93ecb9023af44803328e491c5374136e0813600289582a72d196d
vcpkg_copy_tools 3d45ff761bddbabe8923b52330168dc3abd295fa469d3f2e47cb14dce85332d5
vcpkg_fixup_pkgconfig 1a15f6c6d8e2b244d83a7514a0412d339127d2217d1df60ad1388b546c85f777
vcpkg_from_git 96ed81968f76354c00096dd8cd4e63c6a235fa969334a11ab18d11c0c512ff58
vcpkg_from_github 1284881728e98a182fc63e841be04e39b8c94753fdc361603c72a63c1adcf846
vcpkg_install_copyright ba6c169ab4e59fa05682e530cdeb883767de22c8391f023d4e6844a7ec5dd3d2
vcpkg_replace_string b450deb79207478b37119743e00808ebc42de0628e7b98c14ab24728bd5c78b8
