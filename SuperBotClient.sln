﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{9D31A6EE-4659-3741-8264-2D214C380A13}"
	ProjectSection(ProjectDependencies) = postProject
		{49C5298D-2B24-3FF2-9E56-C650762EE026} = {49C5298D-2B24-3FF2-9E56-C650762EE026}
		{B3F52261-6078-34D3-963F-BF3472B47210} = {B3F52261-6078-34D3-963F-BF3472B47210}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "INSTALL", "INSTALL.vcxproj", "{500A7AB2-352E-34A0-8989-00AD52215FE7}"
	ProjectSection(ProjectDependencies) = postProject
		{9D31A6EE-4659-3741-8264-2D214C380A13} = {9D31A6EE-4659-3741-8264-2D214C380A13}
		{B3F52261-6078-34D3-963F-BF3472B47210} = {B3F52261-6078-34D3-963F-BF3472B47210}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "PACKAGE", "PACKAGE.vcxproj", "{14D94A8F-0285-3B2A-B7E2-02C0F4A3ADC8}"
	ProjectSection(ProjectDependencies) = postProject
		{9D31A6EE-4659-3741-8264-2D214C380A13} = {9D31A6EE-4659-3741-8264-2D214C380A13}
		{B3F52261-6078-34D3-963F-BF3472B47210} = {B3F52261-6078-34D3-963F-BF3472B47210}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "SuperBotClient", "SuperBotClient.vcxproj", "{49C5298D-2B24-3FF2-9E56-C650762EE026}"
	ProjectSection(ProjectDependencies) = postProject
		{B3F52261-6078-34D3-963F-BF3472B47210} = {B3F52261-6078-34D3-963F-BF3472B47210}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "ZERO_CHECK.vcxproj", "{B3F52261-6078-34D3-963F-BF3472B47210}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Release|x64 = Release|x64
		MinSizeRel|x64 = MinSizeRel|x64
		RelWithDebInfo|x64 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{9D31A6EE-4659-3741-8264-2D214C380A13}.Debug|x64.ActiveCfg = Debug|x64
		{9D31A6EE-4659-3741-8264-2D214C380A13}.Debug|x64.Build.0 = Debug|x64
		{9D31A6EE-4659-3741-8264-2D214C380A13}.Release|x64.ActiveCfg = Release|x64
		{9D31A6EE-4659-3741-8264-2D214C380A13}.Release|x64.Build.0 = Release|x64
		{9D31A6EE-4659-3741-8264-2D214C380A13}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{9D31A6EE-4659-3741-8264-2D214C380A13}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{9D31A6EE-4659-3741-8264-2D214C380A13}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{9D31A6EE-4659-3741-8264-2D214C380A13}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{500A7AB2-352E-34A0-8989-00AD52215FE7}.Debug|x64.ActiveCfg = Debug|x64
		{500A7AB2-352E-34A0-8989-00AD52215FE7}.Release|x64.ActiveCfg = Release|x64
		{500A7AB2-352E-34A0-8989-00AD52215FE7}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{500A7AB2-352E-34A0-8989-00AD52215FE7}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{14D94A8F-0285-3B2A-B7E2-02C0F4A3ADC8}.Debug|x64.ActiveCfg = Debug|x64
		{14D94A8F-0285-3B2A-B7E2-02C0F4A3ADC8}.Release|x64.ActiveCfg = Release|x64
		{14D94A8F-0285-3B2A-B7E2-02C0F4A3ADC8}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{14D94A8F-0285-3B2A-B7E2-02C0F4A3ADC8}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{49C5298D-2B24-3FF2-9E56-C650762EE026}.Debug|x64.ActiveCfg = Debug|x64
		{49C5298D-2B24-3FF2-9E56-C650762EE026}.Debug|x64.Build.0 = Debug|x64
		{49C5298D-2B24-3FF2-9E56-C650762EE026}.Release|x64.ActiveCfg = Release|x64
		{49C5298D-2B24-3FF2-9E56-C650762EE026}.Release|x64.Build.0 = Release|x64
		{49C5298D-2B24-3FF2-9E56-C650762EE026}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{49C5298D-2B24-3FF2-9E56-C650762EE026}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{49C5298D-2B24-3FF2-9E56-C650762EE026}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{49C5298D-2B24-3FF2-9E56-C650762EE026}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{B3F52261-6078-34D3-963F-BF3472B47210}.Debug|x64.ActiveCfg = Debug|x64
		{B3F52261-6078-34D3-963F-BF3472B47210}.Debug|x64.Build.0 = Debug|x64
		{B3F52261-6078-34D3-963F-BF3472B47210}.Release|x64.ActiveCfg = Release|x64
		{B3F52261-6078-34D3-963F-BF3472B47210}.Release|x64.Build.0 = Release|x64
		{B3F52261-6078-34D3-963F-BF3472B47210}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{B3F52261-6078-34D3-963F-BF3472B47210}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{B3F52261-6078-34D3-963F-BF3472B47210}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{B3F52261-6078-34D3-963F-BF3472B47210}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {C9C3639B-FF59-3755-9AE4-4A3190E6D71D}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
