# WebRTC Integration Test Plan

## Test Overview

This document outlines the testing strategy for the WebRTC integration in SuperBot.

## Test Categories

### 1. Unit Tests

#### Client Side (C++)
- **WebRTCClient Tests**
  - Initialization and cleanup
  - Configuration validation
  - Connection state management
  - Message serialization/deserialization

- **SignalingClient Tests**
  - WebSocket connection establishment
  - Message queue management
  - Peer discovery functionality
  - Error handling and recovery

#### Server Side (C#)
- **IWebRTCService Tests**
  - Service initialization
  - Peer connection management
  - Data channel operations
  - Statistics tracking

- **SignalingServer Tests**
  - WebSocket server functionality
  - Message routing between peers
  - Peer registration and discovery
  - Connection cleanup

### 2. Integration Tests

#### Signaling Integration
```csharp
[Test]
public async Task TestSignalingHandshake()
{
    // Arrange
    var server = new SignalingServer(logger, config);
    var client = new SignalingClient();
    
    // Act
    await server.StartAsync();
    var connected = await client.ConnectAsync("ws://localhost:8080", "test-peer");
    
    // Assert
    Assert.IsTrue(connected);
    Assert.AreEqual(ConnectionState.Connected, client.GetConnectionState());
}
```

#### WebRTC Connection Establishment
```cpp
TEST(WebRTCClientTest, ConnectionEstablishment)
{
    // Arrange
    WebRTCConfig config;
    config.signalingServerUrl = "ws://localhost";
    config.signalingServerPort = 8080;
    config.enableDataChannels = true;
    
    WebRTCClient client;
    
    // Act
    bool initialized = client.initialize(config);
    bool connected = client.connect("test-peer-id");
    
    // Assert
    EXPECT_TRUE(initialized);
    EXPECT_TRUE(connected);
    EXPECT_EQ(WebRTCConnectionState::CONNECTED, client.getConnectionState());
}
```

#### Data Channel Communication
```csharp
[Test]
public async Task TestDataChannelCommunication()
{
    // Arrange
    var service = serviceProvider.GetService<IWebRTCService>();
    await service.InitializeAsync(webRtcConfig);
    await service.CreatePeerConnectionAsync("peer1");
    
    var testData = Encoding.UTF8.GetBytes("Hello WebRTC!");
    bool dataReceived = false;
    
    service.DataReceived += (sender, args) => {
        dataReceived = true;
        Assert.AreEqual(testData, args.data);
    };
    
    // Act
    await service.SendDataAsync("peer1", testData);
    
    // Wait for data to be received
    await Task.Delay(1000);
    
    // Assert
    Assert.IsTrue(dataReceived);
}
```

### 3. End-to-End Tests

#### Screen Frame Transmission
```cpp
TEST(E2ETest, ScreenFrameTransmission)
{
    // Setup client and server
    WebRTCClient client;
    // ... initialize client
    
    // Create test screen frame
    ScreenFrame frame;
    frame.width = 1920;
    frame.height = 1080;
    frame.format = ScreenFormat::RGB24;
    frame.data.resize(frame.width * frame.height * 3);
    
    // Send frame
    bool sent = client.sendScreenFrame(frame);
    EXPECT_TRUE(sent);
    
    // Verify frame received on server side
    // ... verification logic
}
```

#### Protocol Fallback
```csharp
[Test]
public async Task TestProtocolFallback()
{
    // Arrange
    var adapter = new NetworkServiceAdapter(logger, configuration, serviceProvider);
    
    // Simulate WebRTC failure
    configuration["SuperBot:WebRTC:Enabled"] = "false";
    
    // Act
    var connected = await adapter.ConnectAsync("127.0.0.1", 7878);
    
    // Assert
    Assert.IsTrue(connected);
    // Verify it fell back to TCP
}
```

### 4. Performance Tests

#### Latency Measurement
```cpp
class LatencyTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Setup WebRTC client and server
    }
    
    void TearDown() override {
        // Cleanup
    }
};

TEST_F(LatencyTest, MeasureRoundTripTime)
{
    auto start = std::chrono::high_resolution_clock::now();
    
    // Send ping message
    client.sendMessage(WebRTCMessageType::HEARTBEAT, {});
    
    // Wait for response
    // ... wait logic
    
    auto end = std::chrono::high_resolution_clock::now();
    auto latency = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
    
    EXPECT_LT(latency.count(), 100); // Expect < 100ms latency
}
```

#### Throughput Testing
```csharp
[Test]
public async Task TestDataThroughput()
{
    var service = serviceProvider.GetService<IWebRTCService>();
    await service.InitializeAsync(webRtcConfig);
    
    var dataSize = 1024 * 1024; // 1MB
    var testData = new byte[dataSize];
    new Random().NextBytes(testData);
    
    var stopwatch = Stopwatch.StartNew();
    
    for (int i = 0; i < 100; i++)
    {
        await service.SendDataAsync("peer1", testData);
    }
    
    stopwatch.Stop();
    
    var throughputMbps = (100.0 * dataSize * 8) / (stopwatch.ElapsedMilliseconds * 1000);
    
    Assert.Greater(throughputMbps, 10); // Expect > 10 Mbps
}
```

### 5. Stress Tests

#### Multiple Peer Connections
```csharp
[Test]
public async Task TestMultiplePeerConnections()
{
    var service = serviceProvider.GetService<IWebRTCService>();
    await service.InitializeAsync(webRtcConfig);
    
    var peerCount = 50;
    var tasks = new List<Task>();
    
    for (int i = 0; i < peerCount; i++)
    {
        var peerId = $"peer-{i}";
        tasks.Add(service.CreatePeerConnectionAsync(peerId));
    }
    
    await Task.WhenAll(tasks);
    
    var connectedPeers = await service.GetConnectedPeersAsync();
    Assert.AreEqual(peerCount, connectedPeers.Count);
}
```

#### Memory Leak Detection
```cpp
TEST(StressTest, MemoryLeakDetection)
{
    size_t initialMemory = getCurrentMemoryUsage();
    
    for (int i = 0; i < 1000; i++)
    {
        WebRTCClient client;
        WebRTCConfig config;
        
        client.initialize(config);
        client.connect("test-peer");
        client.disconnect();
        // client destructor called automatically
    }
    
    size_t finalMemory = getCurrentMemoryUsage();
    size_t memoryIncrease = finalMemory - initialMemory;
    
    EXPECT_LT(memoryIncrease, 10 * 1024 * 1024); // < 10MB increase
}
```

### 6. Network Condition Tests

#### Packet Loss Simulation
```csharp
[Test]
public async Task TestPacketLossResilience()
{
    // Setup network simulation with 5% packet loss
    var networkSimulator = new NetworkSimulator();
    networkSimulator.SetPacketLoss(0.05);
    
    var service = serviceProvider.GetService<IWebRTCService>();
    await service.InitializeAsync(webRtcConfig);
    
    // Send data and verify delivery despite packet loss
    var testData = Encoding.UTF8.GetBytes("Test message");
    var success = await service.SendDataAsync("peer1", testData);
    
    Assert.IsTrue(success);
}
```

#### Bandwidth Limitation
```cpp
TEST(NetworkTest, BandwidthLimitation)
{
    // Simulate limited bandwidth (1 Mbps)
    NetworkSimulator::SetBandwidthLimit(1000000);
    
    WebRTCClient client;
    // ... setup client
    
    // Test adaptive bitrate
    auto stats = client.getStatistics();
    EXPECT_LE(stats.bandwidth, 1000000);
}
```

## Test Execution

### Automated Testing
```bash
# Client tests (C++)
cd Client/build
ctest --output-on-failure

# Server tests (C#)
cd Server
dotnet test --logger:console
```

### Manual Testing Checklist

#### Basic Functionality
- [ ] Client can connect to signaling server
- [ ] Peer discovery works correctly
- [ ] WebRTC connection establishes successfully
- [ ] Data channels open and function
- [ ] Screen frames transmit correctly
- [ ] Quality control messages work
- [ ] Graceful disconnection

#### Error Scenarios
- [ ] Network disconnection recovery
- [ ] Signaling server unavailable
- [ ] Invalid peer ID handling
- [ ] Malformed message handling
- [ ] Resource exhaustion scenarios

#### Performance Validation
- [ ] Latency < 100ms in local network
- [ ] Throughput > 10 Mbps for large data
- [ ] CPU usage < 50% during operation
- [ ] Memory usage stable over time
- [ ] No memory leaks detected

### Test Environment Setup

#### Local Testing
```yaml
# docker-compose.test.yml
version: '3.8'
services:
  signaling-server:
    build: ./Server
    ports:
      - "8080:8080"
    environment:
      - ASPNETCORE_ENVIRONMENT=Testing
      
  stun-server:
    image: coturn/coturn
    ports:
      - "3478:3478/udp"
    command: turnserver --listening-port=3478 --realm=test
```

#### CI/CD Integration
```yaml
# .github/workflows/webrtc-tests.yml
name: WebRTC Tests
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      
      - name: Setup .NET
        uses: actions/setup-dotnet@v1
        with:
          dotnet-version: 8.0.x
          
      - name: Setup C++
        run: |
          sudo apt-get update
          sudo apt-get install -y cmake build-essential
          
      - name: Install vcpkg dependencies
        run: |
          cd Client
          vcpkg install
          
      - name: Build and test client
        run: |
          cd Client
          mkdir build && cd build
          cmake .. -DCMAKE_TOOLCHAIN_FILE=../vcpkg/scripts/buildsystems/vcpkg.cmake
          make
          ctest
          
      - name: Build and test server
        run: |
          cd Server
          dotnet test
```

## Success Criteria

### Functional Requirements
- All unit tests pass (100% success rate)
- Integration tests demonstrate end-to-end functionality
- Performance meets or exceeds TCP implementation
- Error handling is robust and graceful

### Performance Requirements
- Latency: < 100ms in local network, < 500ms over internet
- Throughput: > 10 Mbps for bulk data transfer
- CPU Usage: < 50% during normal operation
- Memory Usage: Stable, no leaks detected

### Reliability Requirements
- Connection success rate > 95%
- Automatic recovery from network issues
- Graceful degradation under poor network conditions
- No crashes or hangs during stress testing
