using Microsoft.Extensions.Logging;
using K4os.Compression.LZ4;
using System.IO;

namespace SuperBotServer.Services
{
    public interface ICompressionService
    {
        /// <summary>
        /// Compress data using LZ4 algorithm
        /// </summary>
        byte[] Compress(byte[] data);

        /// <summary>
        /// Decompress LZ4 compressed data
        /// </summary>
        byte[] Decompress(byte[] compressedData);

        /// <summary>
        /// Decompress LZ4 compressed data with known original size
        /// </summary>
        byte[] Decompress(byte[] compressedData, int originalSize);

        /// <summary>
        /// Check if data appears to be compressed
        /// </summary>
        bool IsCompressed(byte[] data);

        /// <summary>
        /// Get compression ratio (compressed size / original size)
        /// </summary>
        double GetCompressionRatio(int originalSize, int compressedSize);

        /// <summary>
        /// Get compression statistics
        /// </summary>
        CompressionStats GetStatistics();

        /// <summary>
        /// Reset compression statistics
        /// </summary>
        void ResetStatistics();
    }

    public class CompressionStats
    {
        public long TotalBytesCompressed { get; set; }
        public long TotalBytesDecompressed { get; set; }
        public long TotalCompressionOperations { get; set; }
        public long TotalDecompressionOperations { get; set; }
        public double AverageCompressionRatio { get; set; }
        public TimeSpan TotalCompressionTime { get; set; }
        public TimeSpan TotalDecompressionTime { get; set; }

        // Alias properties for compatibility
        public long TotalBytesUncompressed
        {
            get => TotalBytesCompressed;
            set => TotalBytesCompressed = value;
        }

        public double CompressionRatio
        {
            get => AverageCompressionRatio;
            set => AverageCompressionRatio = value;
        }

        public double AverageCompressionTime
        {
            get => TotalCompressionTime.TotalMilliseconds / Math.Max(1, TotalCompressionOperations);
        }
    }

    public class CompressionService : ICompressionService
    {
        private readonly ILogger<CompressionService> _logger;
        private readonly CompressionStats _stats = new();
        private readonly object _statsLock = new();

        public CompressionService(ILogger<CompressionService> logger)
        {
            _logger = logger;
        }

        public byte[] Compress(byte[] data)
        {
            if (data == null || data.Length == 0)
                return Array.Empty<byte>();

            var startTime = DateTime.UtcNow;

            try
            {
                // Use LZ4 for better performance with screen data
                var maxCompressedSize = LZ4Codec.MaximumOutputSize(data.Length);
                var compressed = new byte[maxCompressedSize];
                var compressedSize = LZ4Codec.Encode(data, 0, data.Length, compressed, 0, compressed.Length);

                // Resize to actual compressed size
                var result = new byte[compressedSize];
                Array.Copy(compressed, result, compressedSize);

                var compressionTime = DateTime.UtcNow - startTime;

                // Update statistics
                lock (_statsLock)
                {
                    _stats.TotalBytesCompressed += data.Length;
                    _stats.TotalCompressionOperations++;
                    _stats.TotalCompressionTime += compressionTime;

                    // Update average compression ratio
                    var ratio = (double)result.Length / data.Length;
                    _stats.AverageCompressionRatio =
                        (_stats.AverageCompressionRatio * (_stats.TotalCompressionOperations - 1) + ratio) /
                        _stats.TotalCompressionOperations;
                }

                _logger.LogDebug("LZ4 compressed {OriginalSize} bytes to {CompressedSize} bytes (ratio: {Ratio:F2}) in {Time}ms",
                    data.Length, result.Length, (double)result.Length / data.Length, compressionTime.TotalMilliseconds);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to compress data of size {Size}", data.Length);
                return data; // Return original data if compression fails
            }
        }

        public byte[] Decompress(byte[] compressedData)
        {
            if (compressedData == null || compressedData.Length == 0)
                return Array.Empty<byte>();

            var startTime = DateTime.UtcNow;

            try
            {
                // For LZ4, we need to estimate the decompressed size
                // Start with a reasonable estimate and expand if needed
                var estimatedSize = compressedData.Length * 4; // Conservative estimate
                var decompressed = new byte[estimatedSize];

                var decompressedSize = LZ4Codec.Decode(compressedData, 0, compressedData.Length, decompressed, 0, decompressed.Length);

                if (decompressedSize < 0)
                {
                    // Try with larger buffer
                    estimatedSize = compressedData.Length * 10;
                    decompressed = new byte[estimatedSize];
                    decompressedSize = LZ4Codec.Decode(compressedData, 0, compressedData.Length, decompressed, 0, decompressed.Length);
                }

                if (decompressedSize < 0)
                {
                    throw new InvalidDataException("Failed to decompress LZ4 data");
                }

                // Resize to actual decompressed size
                var result = new byte[decompressedSize];
                Array.Copy(decompressed, result, decompressedSize);

                var decompressionTime = DateTime.UtcNow - startTime;

                // Update statistics
                lock (_statsLock)
                {
                    _stats.TotalBytesDecompressed += result.Length;
                    _stats.TotalDecompressionOperations++;
                    _stats.TotalDecompressionTime += decompressionTime;
                }

                _logger.LogDebug("LZ4 decompressed {CompressedSize} bytes to {OriginalSize} bytes in {Time}ms",
                    compressedData.Length, result.Length, decompressionTime.TotalMilliseconds);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to decompress data of size {Size}", compressedData.Length);
                return Array.Empty<byte>();
            }
        }

        public byte[] Decompress(byte[] compressedData, int originalSize)
        {
            if (compressedData == null || compressedData.Length == 0)
            {
                _logger.LogWarning("Decompress called with null or empty compressed data");
                return Array.Empty<byte>();
            }

            if (originalSize <= 0)
            {
                _logger.LogWarning("Decompress called with invalid original size: {OriginalSize}", originalSize);
                return Array.Empty<byte>();
            }

            var startTime = DateTime.UtcNow;

            try
            {
                _logger.LogDebug("LZ4 decompressing {CompressedSize} bytes to expected size {OriginalSize}",
                    compressedData.Length, originalSize);

                // For LZ4 with known original size, we can allocate exact buffer
                var decompressed = new byte[originalSize];
                var decompressedSize = LZ4Codec.Decode(compressedData, 0, compressedData.Length, decompressed, 0, decompressed.Length);

                if (decompressedSize < 0)
                {
                    var errorMsg = $"LZ4 decompression failed with error code: {decompressedSize}";
                    _logger.LogError(errorMsg);
                    throw new InvalidDataException(errorMsg);
                }

                if (decompressedSize != originalSize)
                {
                    _logger.LogError("LZ4 decompressed size mismatch: got {ActualSize}, expected {ExpectedSize} - this indicates data corruption or incorrect original size",
                        decompressedSize, originalSize);

                    // This is a critical error - the original size from client doesn't match actual decompressed size
                    // This could indicate:
                    // 1. Data corruption during transmission
                    // 2. Client sending incorrect original size
                    // 3. Endianness issues with original size field

                    if (decompressedSize > 0 && decompressedSize < decompressed.Length)
                    {
                        // Return the actual decompressed data, but log the discrepancy
                        _logger.LogWarning("Returning actual decompressed data of size {ActualSize} instead of expected {ExpectedSize}",
                            decompressedSize, originalSize);
                        var result = new byte[decompressedSize];
                        Array.Copy(decompressed, result, decompressedSize);
                        return result;
                    }
                    else
                    {
                        throw new InvalidDataException($"Decompressed size {decompressedSize} is invalid or exceeds buffer size");
                    }
                }

                var decompressionTime = DateTime.UtcNow - startTime;

                // Update statistics
                lock (_statsLock)
                {
                    _stats.TotalBytesDecompressed += decompressed.Length;
                    _stats.TotalDecompressionOperations++;
                    _stats.TotalDecompressionTime += decompressionTime;
                }

                _logger.LogDebug("LZ4 successfully decompressed {CompressedSize} bytes to {OriginalSize} bytes (known size) in {Time:F2}ms",
                    compressedData.Length, decompressed.Length, decompressionTime.TotalMilliseconds);

                return decompressed;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to decompress LZ4 data: CompressedSize={CompressedSize}, ExpectedOriginalSize={OriginalSize}",
                    compressedData.Length, originalSize);

                // Log first few bytes of compressed data for debugging
                var previewBytes = compressedData.Take(Math.Min(16, compressedData.Length)).ToArray();
                _logger.LogError("Compressed data preview (first {Count} bytes): {Bytes}",
                    previewBytes.Length, string.Join(" ", previewBytes.Select(b => $"0x{b:X2}")));

                throw; // Re-throw to let caller handle the error
            }
        }

        public bool IsCompressed(byte[] data)
        {
            if (data == null || data.Length < 4)
                return false;

            // For LZ4, we'll use a simple heuristic:
            // Check if the data looks like it could be LZ4 compressed
            // LZ4 doesn't have a standard magic number, so we'll use compression ratio as indicator
            // If data appears to have high entropy or specific patterns, it might be compressed

            // Simple heuristic: if first few bytes have certain patterns typical of LZ4
            // This is not foolproof but works for our use case
            try
            {
                // Try to decompress a small portion to see if it's valid LZ4
                var testBuffer = new byte[Math.Min(1024, data.Length * 4)];
                var result = LZ4Codec.Decode(data, 0, Math.Min(100, data.Length), testBuffer, 0, testBuffer.Length);
                return result > 0;
            }
            catch
            {
                return false;
            }
        }

        public double GetCompressionRatio(int originalSize, int compressedSize)
        {
            if (originalSize == 0) return 0.0;
            return (double)compressedSize / originalSize;
        }

        public CompressionStats GetStatistics()
        {
            lock (_statsLock)
            {
                return new CompressionStats
                {
                    TotalBytesCompressed = _stats.TotalBytesCompressed,
                    TotalBytesDecompressed = _stats.TotalBytesDecompressed,
                    TotalCompressionOperations = _stats.TotalCompressionOperations,
                    TotalDecompressionOperations = _stats.TotalDecompressionOperations,
                    AverageCompressionRatio = _stats.AverageCompressionRatio,
                    TotalCompressionTime = _stats.TotalCompressionTime,
                    TotalDecompressionTime = _stats.TotalDecompressionTime
                };
            }
        }

        public void ResetStatistics()
        {
            lock (_statsLock)
            {
                _stats.TotalBytesCompressed = 0;
                _stats.TotalBytesDecompressed = 0;
                _stats.TotalCompressionOperations = 0;
                _stats.TotalDecompressionOperations = 0;
                _stats.AverageCompressionRatio = 0.0;
                _stats.TotalCompressionTime = TimeSpan.Zero;
                _stats.TotalDecompressionTime = TimeSpan.Zero;
            }
        }
    }
}
