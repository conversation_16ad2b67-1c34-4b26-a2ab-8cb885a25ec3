using SuperBotServer.Models;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;

namespace SuperBotServer.Services
{
    /// <summary>
    /// Adapter that provides a unified interface for both TCP and WebRTC networking
    /// </summary>
    public class NetworkServiceAdapter : INetworkService
    {
        private readonly ILogger<NetworkServiceAdapter> _logger;
        private readonly IConfiguration _configuration;
        private readonly IServiceProvider _serviceProvider;
        
        private INetworkService? _activeService;
        private IWebRTCService? _webRtcService;
        private NetworkService? _tcpService;
        
        private string _currentProtocol = "TCP";
        private bool _disposed;

        public NetworkServiceAdapter(
            ILogger<NetworkServiceAdapter> logger,
            IConfiguration configuration,
            IServiceProvider serviceProvider)
        {
            _logger = logger;
            _configuration = configuration;
            _serviceProvider = serviceProvider;
        }

        // Properties from INetworkService
        public bool IsConnected => _activeService?.IsConnected ?? false;
        public ConnectionInfo? CurrentConnection => _activeService?.CurrentConnection;
        public long BytesSent => _activeService?.BytesSent ?? 0;
        public long BytesReceived => _activeService?.BytesReceived ?? 0;
        public int MessagesSent => _activeService?.MessagesSent ?? 0;
        public int MessagesReceived => _activeService?.MessagesReceived ?? 0;
        public int CurrentLatency => _activeService?.CurrentLatency ?? 0;

        // Events from INetworkService
        public event EventHandler<ConnectionInfo>? ConnectionEstablished;
        public event EventHandler? ConnectionLost;
        public event EventHandler<string>? ConnectionError;
        public event EventHandler<ScreenFrame>? ScreenFrameReceived;
        public event EventHandler<string>? ClipboardTextReceived;
        public event EventHandler<byte[]>? ClipboardImageReceived;
        public event EventHandler<(string operation, double progress)>? FileTransferProgress;
        public event EventHandler? StatisticsUpdated;
        public event EventHandler<QualityStatusMessage>? QualityStatusReceived;

        public async Task<bool> ConnectAsync(string address, int port = 7878, CancellationToken cancellationToken = default)
        {
            try
            {
                // Determine which protocol to use
                var protocol = _configuration["SuperBot:Network:Protocol"] ?? "TCP";
                var webRtcEnabled = _configuration.GetValue<bool>("SuperBot:WebRTC:Enabled", false);

                _logger.LogInformation("Connecting using protocol: {Protocol}, WebRTC enabled: {WebRtcEnabled}", 
                    protocol, webRtcEnabled);

                if (protocol.Equals("WebRTC", StringComparison.OrdinalIgnoreCase) && webRtcEnabled)
                {
                    return await ConnectWebRTCAsync(address, port, cancellationToken);
                }
                else
                {
                    return await ConnectTCPAsync(address, port, cancellationToken);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to connect using adapter");
                ConnectionError?.Invoke(this, ex.Message);
                return false;
            }
        }

        private async Task<bool> ConnectWebRTCAsync(string address, int port, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Initializing WebRTC connection to {Address}:{Port}", address, port);

                // Initialize WebRTC service if not already done
                if (_webRtcService == null)
                {
                    _webRtcService = _serviceProvider.GetRequiredService<IWebRTCService>();
                    
                    // Configure WebRTC
                    var config = new WebRTCConfiguration
                    {
                        SignalingServerUrl = _configuration["SuperBot:WebRTC:SignalingServerUrl"] ?? "ws://localhost",
                        SignalingServerPort = _configuration.GetValue<int>("SuperBot:WebRTC:SignalingServerPort", 8080),
                        IceServers = _configuration.GetSection("SuperBot:WebRTC:IceServers").Get<List<string>>() ?? new(),
                        EnableDataChannels = _configuration.GetValue<bool>("SuperBot:WebRTC:EnableDataChannels", true),
                        ConnectionTimeout = _configuration.GetValue<int>("SuperBot:WebRTC:ConnectionTimeout", 30000),
                        HeartbeatInterval = _configuration.GetValue<int>("SuperBot:WebRTC:HeartbeatInterval", 10000),
                        EnableLogging = _configuration.GetValue<bool>("SuperBot:WebRTC:EnableLogging", true),
                        LogLevel = _configuration["SuperBot:WebRTC:LogLevel"] ?? "Info"
                    };

                    await _webRtcService.InitializeAsync(config, cancellationToken);
                    
                    // Start signaling server
                    await _webRtcService.StartSignalingServerAsync(cancellationToken);
                    
                    // Subscribe to WebRTC events
                    SubscribeToWebRTCEvents();
                }

                // Create peer connection
                var peerId = $"{address}:{port}";
                var success = await _webRtcService.CreatePeerConnectionAsync(peerId, cancellationToken);
                
                if (success)
                {
                    _activeService = new WebRTCNetworkServiceWrapper(_webRtcService, peerId);
                    _currentProtocol = "WebRTC";
                    
                    _logger.LogInformation("WebRTC connection established successfully");
                    
                    // Create connection info
                    var connectionInfo = new ConnectionInfo
                    {
                        Address = address,
                        Port = port,
                        Status = ConnectionStatus.Connected,
                        ConnectionTime = DateTime.Now,
                        Protocol = "WebRTC"
                    };
                    
                    ConnectionEstablished?.Invoke(this, connectionInfo);
                    return true;
                }
                
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to establish WebRTC connection");
                
                // Fallback to TCP if enabled
                var enableFallback = _configuration.GetValue<bool>("SuperBot:WebRTC:EnableFallbackToTCP", true);
                if (enableFallback)
                {
                    _logger.LogInformation("Falling back to TCP connection");
                    return await ConnectTCPAsync(address, port, cancellationToken);
                }
                
                return false;
            }
        }

        private async Task<bool> ConnectTCPAsync(string address, int port, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Initializing TCP connection to {Address}:{Port}", address, port);

                if (_tcpService == null)
                {
                    var securityService = _serviceProvider.GetRequiredService<ISecurityService>();
                    var compressionService = _serviceProvider.GetRequiredService<ICompressionService>();
                    var tcpLogger = _serviceProvider.GetRequiredService<ILogger<NetworkService>>();
                    
                    _tcpService = new NetworkService(tcpLogger, securityService, compressionService);
                    
                    // Subscribe to TCP events
                    SubscribeToTCPEvents();
                }

                var success = await _tcpService.ConnectAsync(address, port, cancellationToken);
                
                if (success)
                {
                    _activeService = _tcpService;
                    _currentProtocol = "TCP";
                    _logger.LogInformation("TCP connection established successfully");
                }
                
                return success;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to establish TCP connection");
                return false;
            }
        }

        private void SubscribeToWebRTCEvents()
        {
            if (_webRtcService == null) return;

            _webRtcService.PeerConnected += (sender, peerInfo) =>
            {
                var connectionInfo = new ConnectionInfo
                {
                    Address = peerInfo.RemoteAddress,
                    Port = 0, // WebRTC doesn't use traditional ports
                    Status = ConnectionStatus.Connected,
                    ConnectionTime = peerInfo.ConnectedTime,
                    Protocol = "WebRTC"
                };
                ConnectionEstablished?.Invoke(this, connectionInfo);
            };

            _webRtcService.PeerDisconnected += (sender, peerId) =>
            {
                ConnectionLost?.Invoke(this, EventArgs.Empty);
            };

            _webRtcService.PeerError += (sender, args) =>
            {
                ConnectionError?.Invoke(this, args.error);
            };

            _webRtcService.ScreenFrameReceived += (sender, args) =>
            {
                ScreenFrameReceived?.Invoke(this, args.frame);
            };

            _webRtcService.QualityStatusReceived += (sender, args) =>
            {
                QualityStatusReceived?.Invoke(this, args.status);
            };
        }

        private void SubscribeToTCPEvents()
        {
            if (_tcpService == null) return;

            _tcpService.ConnectionEstablished += (sender, info) => ConnectionEstablished?.Invoke(sender, info);
            _tcpService.ConnectionLost += (sender, args) => ConnectionLost?.Invoke(sender, args);
            _tcpService.ConnectionError += (sender, error) => ConnectionError?.Invoke(sender, error);
            _tcpService.ScreenFrameReceived += (sender, frame) => ScreenFrameReceived?.Invoke(sender, frame);
            _tcpService.ClipboardTextReceived += (sender, text) => ClipboardTextReceived?.Invoke(sender, text);
            _tcpService.ClipboardImageReceived += (sender, data) => ClipboardImageReceived?.Invoke(sender, data);
            _tcpService.FileTransferProgress += (sender, progress) => FileTransferProgress?.Invoke(sender, progress);
            _tcpService.StatisticsUpdated += (sender, args) => StatisticsUpdated?.Invoke(sender, args);
            _tcpService.QualityStatusReceived += (sender, status) => QualityStatusReceived?.Invoke(sender, status);
        }

        // Delegate all other INetworkService methods to the active service
        public async Task DisconnectAsync()
        {
            if (_activeService != null)
            {
                await _activeService.DisconnectAsync();
            }
        }

        public async Task SendMessageAsync(MessageType type, byte[] payload, MessageFlags flags = MessageFlags.None)
        {
            if (_activeService != null)
            {
                await _activeService.SendMessageAsync(type, payload, flags);
            }
        }

        public async Task SendQualityControlAsync(QualityControlMessage qualityControl)
        {
            if (_activeService != null)
            {
                await _activeService.SendQualityControlAsync(qualityControl);
            }
        }

        public async Task RequestQualityStatusAsync()
        {
            if (_activeService != null)
            {
                await _activeService.RequestQualityStatusAsync();
            }
        }

        // Implement remaining INetworkService methods by delegating to active service
        public Task SendMouseEventAsync(int x, int y, int button, bool pressed) => 
            _activeService?.SendMouseEventAsync(x, y, button, pressed) ?? Task.CompletedTask;

        public Task SendKeyboardEventAsync(int keyCode, bool pressed, int modifiers = 0) => 
            _activeService?.SendKeyboardEventAsync(keyCode, pressed, modifiers) ?? Task.CompletedTask;

        public Task SendTextAsync(string text) => 
            _activeService?.SendTextAsync(text) ?? Task.CompletedTask;

        public Task RequestScreenInfoAsync() => 
            _activeService?.RequestScreenInfoAsync() ?? Task.CompletedTask;

        public Task<byte[]?> GetLatestScreenFrameAsync() => 
            _activeService?.GetLatestScreenFrameAsync() ?? Task.FromResult<byte[]?>(null);

        public Task<bool> SendFileAsync(string localPath, string remotePath, IProgress<double>? progress = null) => 
            _activeService?.SendFileAsync(localPath, remotePath, progress) ?? Task.FromResult(false);

        public Task<bool> ReceiveFileAsync(string remotePath, string localPath, IProgress<double>? progress = null) => 
            _activeService?.ReceiveFileAsync(remotePath, localPath, progress) ?? Task.FromResult(false);

        public Task SendClipboardTextAsync(string text) => 
            _activeService?.SendClipboardTextAsync(text) ?? Task.CompletedTask;

        public Task SendClipboardImageAsync(byte[] imageData) => 
            _activeService?.SendClipboardImageAsync(imageData) ?? Task.CompletedTask;

        public void SetCompressionEnabled(bool enabled) => _activeService?.SetCompressionEnabled(enabled);
        public void SetEncryptionEnabled(bool enabled) => _activeService?.SetEncryptionEnabled(enabled);
        public void SetHeartbeatInterval(int milliseconds) => _activeService?.SetHeartbeatInterval(milliseconds);
        public void ResetStatistics() => _activeService?.ResetStatistics();
        public CompressionStats GetCompressionStatistics() => _activeService?.GetCompressionStatistics() ?? new CompressionStats();

        public void Dispose()
        {
            if (!_disposed)
            {
                _activeService?.Dispose();
                _webRtcService?.Dispose();
                _tcpService?.Dispose();
                _disposed = true;
            }
        }
    }

    /// <summary>
    /// Wrapper to make WebRTC service compatible with INetworkService interface
    /// </summary>
    internal class WebRTCNetworkServiceWrapper : INetworkService
    {
        private readonly IWebRTCService _webRtcService;
        private readonly string _peerId;

        public WebRTCNetworkServiceWrapper(IWebRTCService webRtcService, string peerId)
        {
            _webRtcService = webRtcService;
            _peerId = peerId;
        }

        public bool IsConnected => _webRtcService.IsPeerConnectedAsync(_peerId).Result;
        public ConnectionInfo? CurrentConnection { get; private set; }
        public long BytesSent => (long)(_webRtcService.GetStatisticsAsync(_peerId).Result?.BytesSent ?? 0);
        public long BytesReceived => (long)(_webRtcService.GetStatisticsAsync(_peerId).Result?.BytesReceived ?? 0);
        public int MessagesSent => (int)(_webRtcService.GetStatisticsAsync(_peerId).Result?.MessagesSent ?? 0);
        public int MessagesReceived => (int)(_webRtcService.GetStatisticsAsync(_peerId).Result?.MessagesReceived ?? 0);
        public int CurrentLatency => (int)(_webRtcService.GetStatisticsAsync(_peerId).Result?.RoundTripTime ?? 0);

        // Events - these will be handled by the adapter
        public event EventHandler<ConnectionInfo>? ConnectionEstablished;
        public event EventHandler? ConnectionLost;
        public event EventHandler<string>? ConnectionError;
        public event EventHandler<ScreenFrame>? ScreenFrameReceived;
        public event EventHandler<string>? ClipboardTextReceived;
        public event EventHandler<byte[]>? ClipboardImageReceived;
        public event EventHandler<(string operation, double progress)>? FileTransferProgress;
        public event EventHandler? StatisticsUpdated;
        public event EventHandler<QualityStatusMessage>? QualityStatusReceived;

        // Implement INetworkService methods by delegating to WebRTC service
        public Task<bool> ConnectAsync(string address, int port = 7878, CancellationToken cancellationToken = default) => 
            Task.FromResult(true); // Already connected through adapter

        public async Task DisconnectAsync() => await _webRtcService.ClosePeerConnectionAsync(_peerId);

        public async Task SendMessageAsync(MessageType type, byte[] payload, MessageFlags flags = MessageFlags.None)
        {
            var webRtcType = MapToWebRTCMessageType(type);
            await _webRtcService.SendMessageAsync(_peerId, webRtcType, payload);
        }

        public async Task SendQualityControlAsync(QualityControlMessage qualityControl) => 
            await _webRtcService.SendQualityControlAsync(_peerId, qualityControl);

        public async Task RequestQualityStatusAsync() => 
            await _webRtcService.RequestQualityStatusAsync(_peerId);

        // Map traditional message types to WebRTC message types
        private WebRTCMessageType MapToWebRTCMessageType(MessageType type)
        {
            return type switch
            {
                MessageType.ScreenFrame => WebRTCMessageType.ScreenFrame,
                MessageType.Heartbeat => WebRTCMessageType.Heartbeat,
                MessageType.QualityControl => WebRTCMessageType.QualityControl,
                MessageType.ErrorResponse => WebRTCMessageType.ErrorResponse,
                _ => WebRTCMessageType.ScreenFrame
            };
        }

        // Placeholder implementations for other methods
        public Task SendMouseEventAsync(int x, int y, int button, bool pressed) => Task.CompletedTask;
        public Task SendKeyboardEventAsync(int keyCode, bool pressed, int modifiers = 0) => Task.CompletedTask;
        public Task SendTextAsync(string text) => Task.CompletedTask;
        public Task RequestScreenInfoAsync() => Task.CompletedTask;
        public Task<byte[]?> GetLatestScreenFrameAsync() => Task.FromResult<byte[]?>(null);
        public Task<bool> SendFileAsync(string localPath, string remotePath, IProgress<double>? progress = null) => Task.FromResult(false);
        public Task<bool> ReceiveFileAsync(string remotePath, string localPath, IProgress<double>? progress = null) => Task.FromResult(false);
        public Task SendClipboardTextAsync(string text) => Task.CompletedTask;
        public Task SendClipboardImageAsync(byte[] imageData) => Task.CompletedTask;
        public void SetCompressionEnabled(bool enabled) { }
        public void SetEncryptionEnabled(bool enabled) { }
        public void SetHeartbeatInterval(int milliseconds) { }
        public void ResetStatistics() => _webRtcService.ResetStatisticsAsync(_peerId);
        public CompressionStats GetCompressionStatistics() => new CompressionStats();

        public void Dispose() { }
    }
}
