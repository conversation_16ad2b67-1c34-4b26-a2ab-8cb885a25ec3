
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineSystem.cmake:205 (message)"
      - "CMakeLists.txt:4 (project)"
    message: |
      The system is: Windows - 10.0.26100 - AMD64
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:4 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      MSBuild version 17.9.8+b34f75857 for .NET Framework
      Build started 25/05/2025 1:09:06 am.
      
      Project "D:\\Project\\SuperBot\\CMakeFiles\\4.0.2\\CompilerIdCXX\\CompilerIdCXX.vcxproj" on node 1 (default targets).
      PrepareForBuild:
        Creating directory "Debug\\".
        Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
        Creating directory "Debug\\CompilerIdCXX.tlog\\".
      InitializeBuildStatus:
        Creating "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
        Touching "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild".
      VcpkgTripletSelection:
        Using triplet "x64-windows" from "D:\\Project\\SuperBot\\Client\\vcpkg\\installed\\x64-windows\\"
        Using normalized configuration "Release"
      ClCompile:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.39.33519\\bin\\HostX64\\x64\\CL.exe /c /I"D:\\Project\\SuperBot\\Client\\vcpkg\\installed\\x64-windows\\include" /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc143.pdb" /external:W0 /Gd /TP /FC /errorReport:queue CMakeCXXCompilerId.cpp
        CMakeCXXCompilerId.cpp
      Link:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.39.33519\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdCXX.exe" /INCREMENTAL:NO /NOLOGO /LIBPATH:"D:\\Project\\SuperBot\\Client\\vcpkg\\installed\\x64-windows\\lib" /LIBPATH:"D:\\Project\\SuperBot\\Client\\vcpkg\\installed\\x64-windows\\lib\\manual-link" kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib "D:\\Project\\SuperBot\\Client\\vcpkg\\installed\\x64-windows\\lib\\*.lib" /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdCXX.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdCXX.lib" /MACHINE:X64 Debug\\CMakeCXXCompilerId.obj
        CompilerIdCXX.vcxproj -> D:\\Project\\SuperBot\\CMakeFiles\\4.0.2\\CompilerIdCXX\\CompilerIdCXX.exe
      AppLocalFromInstalled:
        pwsh.exe -ExecutionPolicy Bypass -noprofile -File "D:\\Project\\SuperBot\\Client\\vcpkg\\scripts\\buildsystems\\msbuild\\applocal.ps1" "D:\\Project\\SuperBot\\CMakeFiles\\4.0.2\\CompilerIdCXX\\CompilerIdCXX.exe" "D:\\Project\\SuperBot\\Client\\vcpkg\\installed\\x64-windows\\bin" "Debug\\CompilerIdCXX.tlog\\CompilerIdCXX.write.1u.tlog" "Debug\\vcpkg.applocal.log"
        'pwsh.exe' is not recognized as an internal or external command,
        operable program or batch file.
        The command "pwsh.exe -ExecutionPolicy Bypass -noprofile -File "D:\\Project\\SuperBot\\Client\\vcpkg\\scripts\\buildsystems\\msbuild\\applocal.ps1" "D:\\Project\\SuperBot\\CMakeFiles\\4.0.2\\CompilerIdCXX\\CompilerIdCXX.exe" "D:\\Project\\SuperBot\\Client\\vcpkg\\installed\\x64-windows\\bin" "Debug\\CompilerIdCXX.tlog\\CompilerIdCXX.write.1u.tlog" "Debug\\vcpkg.applocal.log"" exited with code 9009.
        "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\powershell.exe" -ExecutionPolicy Bypass -noprofile -File "D:\\Project\\SuperBot\\Client\\vcpkg\\scripts\\buildsystems\\msbuild\\applocal.ps1" "D:\\Project\\SuperBot\\CMakeFiles\\4.0.2\\CompilerIdCXX\\CompilerIdCXX.exe" "D:\\Project\\SuperBot\\Client\\vcpkg\\installed\\x64-windows\\bin" "Debug\\CompilerIdCXX.tlog\\CompilerIdCXX.write.1u.tlog" "Debug\\vcpkg.applocal.log"
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_CXX_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_CXX_COMPILER=C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.39.33519\\bin\\Hostx64\\x64\\cl.exe
      FinalizeBuildStatus:
        Deleting file "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild".
        Touching "Debug\\CompilerIdCXX.tlog\\CompilerIdCXX.lastbuildstate".
      Done Building Project "D:\\Project\\SuperBot\\CMakeFiles\\4.0.2\\CompilerIdCXX\\CompilerIdCXX.vcxproj" (default targets).
      
      Build succeeded.
          0 Warning(s)
          0 Error(s)
      
      Time Elapsed 00:00:01.89
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.exe"
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.vcxproj"
      
      The CXX compiler identification is MSVC, found in:
        D:/Project/SuperBot/CMakeFiles/4.0.2/CompilerIdCXX/CompilerIdCXX.exe
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:4 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "D:/Project/SuperBot/CMakeFiles/CMakeScratch/TryCompile-mkvav9"
      binary: "D:/Project/SuperBot/CMakeFiles/CMakeScratch/TryCompile-mkvav9"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'D:/Project/SuperBot/CMakeFiles/CMakeScratch/TryCompile-mkvav9'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_51e59.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.9.8+b34f75857 for .NET Framework
        Build started 25/05/2025 1:09:08 am.
        
        Project "D:\\Project\\SuperBot\\CMakeFiles\\CMakeScratch\\TryCompile-mkvav9\\cmTC_51e59.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_51e59.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "D:\\Project\\SuperBot\\CMakeFiles\\CMakeScratch\\TryCompile-mkvav9\\Debug\\".
          Creating directory "cmTC_51e59.dir\\Debug\\cmTC_51e59.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_51e59.dir\\Debug\\cmTC_51e59.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_51e59.dir\\Debug\\cmTC_51e59.tlog\\unsuccessfulbuild".
        VcpkgTripletSelection:
          Using triplet "x64-windows" from "D:\\Project\\SuperBot\\Client\\vcpkg\\installed\\x64-windows\\"
          Using normalized configuration "Debug"
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.39.33519\\bin\\HostX64\\x64\\CL.exe /c /I"D:\\Project\\SuperBot\\Client\\vcpkg\\installed\\x64-windows\\include" /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_51e59.dir\\Debug\\\\" /Fd"cmTC_51e59.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-4.0\\Modules\\CMakeCXXCompilerABI.cpp"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.39.33523 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /I"D:\\Project\\SuperBot\\Client\\vcpkg\\installed\\x64-windows\\include" /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_51e59.dir\\Debug\\\\" /Fd"cmTC_51e59.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-4.0\\Modules\\CMakeCXXCompilerABI.cpp"
          CMakeCXXCompilerABI.cpp
        C:\\Program Files\\CMake\\share\\cmake-4.0\\Modules\\CMakeCXXCompilerABI.cpp(1,1): error C1090: PDB API call failed, error code '3': D:\\Project\\SuperBot\\CMakeFiles\\CMakeScratch\\TryCompile-mkvav9\\cmTC_51e59.dir\\Debug\\vc143.pdb [D:\\Project\\SuperBot\\CMakeFiles\\CMakeScratch\\TryCompile-mkvav9\\cmTC_51e59.vcxproj]
        Done Building Project "D:\\Project\\SuperBot\\CMakeFiles\\CMakeScratch\\TryCompile-mkvav9\\cmTC_51e59.vcxproj" (default targets) -- FAILED.
        
        Build FAILED.
        
        "D:\\Project\\SuperBot\\CMakeFiles\\CMakeScratch\\TryCompile-mkvav9\\cmTC_51e59.vcxproj" (default target) (1) ->
        (ClCompile target) -> 
          C:\\Program Files\\CMake\\share\\cmake-4.0\\Modules\\CMakeCXXCompilerABI.cpp(1,1): error C1090: PDB API call failed, error code '3': D:\\Project\\SuperBot\\CMakeFiles\\CMakeScratch\\TryCompile-mkvav9\\cmTC_51e59.dir\\Debug\\vc143.pdb [D:\\Project\\SuperBot\\CMakeFiles\\CMakeScratch\\TryCompile-mkvav9\\cmTC_51e59.vcxproj]
        
            0 Warning(s)
            1 Error(s)
        
        Time Elapsed 00:00:00.38
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeTestCXXCompiler.cmake:56 (try_compile)"
      - "CMakeLists.txt:4 (project)"
    checks:
      - "Check for working CXX compiler: C:/Program Files/Microsoft Visual Studio/2022/Professional/VC/Tools/MSVC/14.39.33519/bin/Hostx64/x64/cl.exe"
    directories:
      source: "D:/Project/SuperBot/CMakeFiles/CMakeScratch/TryCompile-4hokp8"
      binary: "D:/Project/SuperBot/CMakeFiles/CMakeScratch/TryCompile-4hokp8"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_CXX_COMPILER_WORKS"
      cached: true
      stdout: |
        Change Dir: 'D:/Project/SuperBot/CMakeFiles/CMakeScratch/TryCompile-4hokp8'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_ae688.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.9.8+b34f75857 for .NET Framework
        Build started 25/05/2025 1:09:09 am.
        
        Project "D:\\Project\\SuperBot\\CMakeFiles\\CMakeScratch\\TryCompile-4hokp8\\cmTC_ae688.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_ae688.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "D:\\Project\\SuperBot\\CMakeFiles\\CMakeScratch\\TryCompile-4hokp8\\Debug\\".
          Creating directory "cmTC_ae688.dir\\Debug\\cmTC_ae688.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_ae688.dir\\Debug\\cmTC_ae688.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_ae688.dir\\Debug\\cmTC_ae688.tlog\\unsuccessfulbuild".
        VcpkgTripletSelection:
          Using triplet "x64-windows" from "D:\\Project\\SuperBot\\Client\\vcpkg\\installed\\x64-windows\\"
          Using normalized configuration "Debug"
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.39.33519\\bin\\HostX64\\x64\\CL.exe /c /I"D:\\Project\\SuperBot\\Client\\vcpkg\\installed\\x64-windows\\include" /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_ae688.dir\\Debug\\\\" /Fd"cmTC_ae688.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "D:\\Project\\SuperBot\\CMakeFiles\\CMakeScratch\\TryCompile-4hokp8\\testCXXCompiler.cxx"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.39.33523 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /I"D:\\Project\\SuperBot\\Client\\vcpkg\\installed\\x64-windows\\include" /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_ae688.dir\\Debug\\\\" /Fd"cmTC_ae688.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "D:\\Project\\SuperBot\\CMakeFiles\\CMakeScratch\\TryCompile-4hokp8\\testCXXCompiler.cxx"
          testCXXCompiler.cxx
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.39.33519\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"D:\\Project\\SuperBot\\CMakeFiles\\CMakeScratch\\TryCompile-4hokp8\\Debug\\cmTC_ae688.exe" /INCREMENTAL /ILK:"cmTC_ae688.dir\\Debug\\cmTC_ae688.ilk" /NOLOGO /LIBPATH:"D:\\Project\\SuperBot\\Client\\vcpkg\\installed\\x64-windows\\debug\\lib" /LIBPATH:"D:\\Project\\SuperBot\\Client\\vcpkg\\installed\\x64-windows\\debug\\lib\\manual-link" kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib "D:\\Project\\SuperBot\\Client\\vcpkg\\installed\\x64-windows\\debug\\lib\\*.lib" /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"D:/Project/SuperBot/CMakeFiles/CMakeScratch/TryCompile-4hokp8/Debug/cmTC_ae688.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"D:/Project/SuperBot/CMakeFiles/CMakeScratch/TryCompile-4hokp8/Debug/cmTC_ae688.lib" /MACHINE:X64  /machine:x64 cmTC_ae688.dir\\Debug\\testCXXCompiler.obj
          cmTC_ae688.vcxproj -> D:\\Project\\SuperBot\\CMakeFiles\\CMakeScratch\\TryCompile-4hokp8\\Debug\\cmTC_ae688.exe
        AppLocalFromInstalled:
          pwsh.exe -ExecutionPolicy Bypass -noprofile -File "D:\\Project\\SuperBot\\Client\\vcpkg\\scripts\\buildsystems\\msbuild\\applocal.ps1" "D:\\Project\\SuperBot\\CMakeFiles\\CMakeScratch\\TryCompile-4hokp8\\Debug\\cmTC_ae688.exe" "D:\\Project\\SuperBot\\Client\\vcpkg\\installed\\x64-windows\\debug\\bin" "cmTC_ae688.dir\\Debug\\cmTC_ae688.tlog\\cmTC_ae688.write.1u.tlog" "cmTC_ae688.dir\\Debug\\vcpkg.applocal.log"
          'pwsh.exe' is not recognized as an internal or external command,
          operable program or batch file.
          The command "pwsh.exe -ExecutionPolicy Bypass -noprofile -File "D:\\Project\\SuperBot\\Client\\vcpkg\\scripts\\buildsystems\\msbuild\\applocal.ps1" "D:\\Project\\SuperBot\\CMakeFiles\\CMakeScratch\\TryCompile-4hokp8\\Debug\\cmTC_ae688.exe" "D:\\Project\\SuperBot\\Client\\vcpkg\\installed\\x64-windows\\debug\\bin" "cmTC_ae688.dir\\Debug\\cmTC_ae688.tlog\\cmTC_ae688.write.1u.tlog" "cmTC_ae688.dir\\Debug\\vcpkg.applocal.log"" exited with code 9009.
          "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\powershell.exe" -ExecutionPolicy Bypass -noprofile -File "D:\\Project\\SuperBot\\Client\\vcpkg\\scripts\\buildsystems\\msbuild\\applocal.ps1" "D:\\Project\\SuperBot\\CMakeFiles\\CMakeScratch\\TryCompile-4hokp8\\Debug\\cmTC_ae688.exe" "D:\\Project\\SuperBot\\Client\\vcpkg\\installed\\x64-windows\\debug\\bin" "cmTC_ae688.dir\\Debug\\cmTC_ae688.tlog\\cmTC_ae688.write.1u.tlog" "cmTC_ae688.dir\\Debug\\vcpkg.applocal.log"
        FinalizeBuildStatus:
          Deleting file "cmTC_ae688.dir\\Debug\\cmTC_ae688.tlog\\unsuccessfulbuild".
          Touching "cmTC_ae688.dir\\Debug\\cmTC_ae688.tlog\\cmTC_ae688.lastbuildstate".
        Done Building Project "D:\\Project\\SuperBot\\CMakeFiles\\CMakeScratch\\TryCompile-4hokp8\\cmTC_ae688.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:01.47
        
      exitCode: 0
...
