using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.Logging;
using SuperBotServer.Models;
using SuperBotServer.Services;
using SuperBotServer.Views;
using System.Collections.ObjectModel;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows;

namespace SuperBotServer.ViewModels
{
    public partial class MainViewModel : ObservableObject
    {
        private readonly ILogger<MainViewModel> _logger;
        private readonly INetworkService _networkService;
        private readonly ISecurityService _securityService;
        private readonly IScreenDisplayService _screenDisplayService;
        private readonly IInputService _inputService;
        private readonly IInputHandlerService _inputHandlerService;
        private readonly IStatisticsService _statisticsService;

        [ObservableProperty]
        private string _quickConnectAddress = "127.0.0.1";

        [ObservableProperty]
        private string _statusMessage = "Ready";

        [ObservableProperty]
        private Brush _statusBrush = Brushes.Gray;

        [ObservableProperty]
        private BitmapSource? _remoteScreenImage;

        // Quality Control Properties
        [ObservableProperty]
        private float _imageScalePercent = 0.5f; // 50% by default

        [ObservableProperty]
        private bool _resizeEnabled = true;

        [ObservableProperty]
        private int _compressionQuality = 80;

        [ObservableProperty]
        private bool _compressionEnabled = true;

        [ObservableProperty]
        private int _frameRate = 30;

        [ObservableProperty]
        private string _qualityStatusText = "Chưa có thông tin";

        [ObservableProperty]
        private bool _isConnected;

        [ObservableProperty]
        private bool _isConnecting;

        [ObservableProperty]
        private bool _isNotConnected = true;

        [ObservableProperty]
        private bool _inputControlEnabled = true;

        [ObservableProperty]
        private string _inputControlIcon = "Mouse";

        [ObservableProperty]
        private string _connectionStatusText = "Disconnected";

        [ObservableProperty]
        private Brush _connectionStatusBrush = Brushes.Red;

        [ObservableProperty]
        private string _networkStatistics = "0 B/s ↑ 0 B/s ↓";

        [ObservableProperty]
        private string _currentTime = DateTime.Now.ToString("HH:mm:ss");

        [ObservableProperty]
        private bool _isFullScreen = false;

        public ObservableCollection<ConnectionInfo> ActiveConnections { get; } = new();
        public IInputHandlerService InputHandler => _inputHandlerService;

        public MainViewModel(
            ILogger<MainViewModel> logger,
            INetworkService networkService,
            ISecurityService securityService,
            IScreenDisplayService screenDisplayService,
            IInputService inputService,
            IInputHandlerService inputHandlerService,
            IStatisticsService statisticsService)
        {
            _logger = logger;
            _networkService = networkService;
            _securityService = securityService;
            _screenDisplayService = screenDisplayService;
            _inputService = inputService;
            _inputHandlerService = inputHandlerService;
            _statisticsService = statisticsService;

            // Subscribe to network service events
            _networkService.ConnectionEstablished += OnConnectionEstablished;
            _networkService.ConnectionLost += OnConnectionLost;
            _networkService.ConnectionError += OnConnectionError;
            _networkService.ScreenFrameReceived += OnScreenFrameReceived;
            _networkService.StatisticsUpdated += OnStatisticsUpdated;
            _networkService.QualityStatusReceived += OnQualityStatusReceived;

            // Start timers
            StartTimers();

            _logger.LogInformation("MainViewModel initialized");
        }



        [RelayCommand]
        private async Task QuickConnectAsync()
        {
            try
            {
                if (string.IsNullOrWhiteSpace(QuickConnectAddress))
                {
                    StatusMessage = "Please enter an IP address";
                    StatusBrush = Brushes.Orange;
                    return;
                }

                _logger.LogInformation("Quick connecting to {Address}", QuickConnectAddress);

                IsConnecting = true;
                IsNotConnected = false;
                StatusMessage = $"Connecting to {QuickConnectAddress}...";
                StatusBrush = Brushes.Orange;

                var success = await _networkService.ConnectAsync(QuickConnectAddress);

                if (!success)
                {
                    IsConnecting = false;
                    IsNotConnected = true;
                    StatusMessage = "Connection failed";
                    StatusBrush = Brushes.Red;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Quick connect failed");
                IsConnecting = false;
                IsNotConnected = true;
                StatusMessage = $"Connection error: {ex.Message}";
                StatusBrush = Brushes.Red;
            }
        }

        [RelayCommand]
        private async Task DisconnectAsync()
        {
            try
            {
                _logger.LogInformation("Disconnecting from remote host");

                await _networkService.DisconnectAsync();

                IsConnected = false;
                IsConnecting = false;
                IsNotConnected = true;
                RemoteScreenImage = null;

                StatusMessage = "Disconnected";
                StatusBrush = Brushes.Gray;
                ConnectionStatusText = "Disconnected";
                ConnectionStatusBrush = Brushes.Red;

                ActiveConnections.Clear();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Disconnect failed");
                StatusMessage = $"Disconnect error: {ex.Message}";
                StatusBrush = Brushes.Red;
            }
        }

        [RelayCommand]
        private void ToggleInputControl()
        {
            try
            {
                InputControlEnabled = !InputControlEnabled;
                InputControlIcon = InputControlEnabled ? "Mouse" : "MouseOff";

                _inputService.SetInputEnabled(InputControlEnabled);
                _inputHandlerService.IsInputEnabled = InputControlEnabled;

                StatusMessage = InputControlEnabled ? "Input control enabled" : "Input control disabled";
                StatusBrush = InputControlEnabled ? Brushes.Green : Brushes.Orange;

                _logger.LogInformation("Input control {Status}", InputControlEnabled ? "enabled" : "disabled");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to toggle input control");
            }
        }

        [RelayCommand]
        private void ToggleFullScreen()
        {
            try
            {
                IsFullScreen = !IsFullScreen;
                _logger.LogInformation("Toggle full screen requested: {IsFullScreen}", IsFullScreen);
                StatusMessage = IsFullScreen ? "Entered full screen mode" : "Exited full screen mode";
                StatusBrush = Brushes.Blue;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to toggle full screen");
            }
        }

        [RelayCommand]
        private void OpenSettings()
        {
            try
            {
                _logger.LogInformation("Opening settings window");
                var settingsWindow = new SettingsWindow();
                settingsWindow.ShowDialog();
                StatusMessage = "Settings opened";
                StatusBrush = Brushes.Blue;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to open settings");
            }
        }

        [RelayCommand]
        private void ShowStatistics()
        {
            try
            {
                _logger.LogInformation("Opening statistics window");
                var statisticsWindow = new StatisticsWindow();
                statisticsWindow.ShowDialog();
                StatusMessage = "Statistics opened";
                StatusBrush = Brushes.Blue;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to show statistics");
            }
        }

        [RelayCommand]
        private void ShowAbout()
        {
            try
            {
                _logger.LogInformation("Opening about dialog");
                // TODO: Show about dialog
                StatusMessage = "About dialog opened";
                StatusBrush = Brushes.Blue;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to show about dialog");
            }
        }

        [RelayCommand]
        private void NewConnection()
        {
            try
            {
                _logger.LogInformation("Opening new connection dialog");
                var connectionDialog = new ConnectionDialog();
                connectionDialog.ShowDialog();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to open connection dialog");
            }
        }

        [RelayCommand]
        private void Exit()
        {
            try
            {
                _logger.LogInformation("Application exit requested");
                Application.Current.Shutdown();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to exit application");
            }
        }

        [RelayCommand]
        private async Task SendQualityControlAsync()
        {
            try
            {
                if (!IsConnected)
                {
                    StatusMessage = "Không có kết nối để gửi cài đặt chất lượng";
                    StatusBrush = Brushes.Orange;
                    return;
                }

                var qualityControl = new QualityControlMessage
                {
                    ImageScalePercent = ImageScalePercent,
                    ResizeEnabled = ResizeEnabled,
                    CompressionQuality = CompressionQuality,
                    CompressionEnabled = CompressionEnabled,
                    FrameRate = FrameRate
                };

                await _networkService.SendQualityControlAsync(qualityControl);

                StatusMessage = $"Đã gửi cài đặt: {(int)(ImageScalePercent * 100)}% scale, {FrameRate} FPS";
                StatusBrush = Brushes.Green;

                _logger.LogInformation("Quality control sent: Scale={Scale}%, FPS={FPS}",
                    (int)(ImageScalePercent * 100), FrameRate);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send quality control");
                StatusMessage = $"Lỗi gửi cài đặt: {ex.Message}";
                StatusBrush = Brushes.Red;
            }
        }

        [RelayCommand]
        private async Task RequestQualityStatusAsync()
        {
            try
            {
                if (!IsConnected)
                {
                    StatusMessage = "Không có kết nối để yêu cầu trạng thái";
                    StatusBrush = Brushes.Orange;
                    return;
                }

                await _networkService.RequestQualityStatusAsync();
                StatusMessage = "Đã yêu cầu trạng thái chất lượng từ client";
                StatusBrush = Brushes.Blue;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to request quality status");
                StatusMessage = $"Lỗi yêu cầu trạng thái: {ex.Message}";
                StatusBrush = Brushes.Red;
            }
        }

        private void OnConnectionEstablished(object? sender, ConnectionInfo connectionInfo)
        {
            Application.Current.Dispatcher.Invoke(() =>
            {
                IsConnected = true;
                IsConnecting = false;
                IsNotConnected = false;

                StatusMessage = $"Connected to {connectionInfo.Address}";
                StatusBrush = Brushes.Green;
                ConnectionStatusText = "Connected";
                ConnectionStatusBrush = Brushes.Green;

                ActiveConnections.Add(connectionInfo);

                _logger.LogInformation("Connection established to {Address}", connectionInfo.Address);
            });
        }

        private void OnConnectionLost(object? sender, EventArgs e)
        {
            Application.Current.Dispatcher.Invoke(() =>
            {
                IsConnected = false;
                IsConnecting = false;
                IsNotConnected = true;
                RemoteScreenImage = null;

                StatusMessage = "Connection lost";
                StatusBrush = Brushes.Red;
                ConnectionStatusText = "Disconnected";
                ConnectionStatusBrush = Brushes.Red;

                ActiveConnections.Clear();

                _logger.LogWarning("Connection lost");
            });
        }

        private void OnConnectionError(object? sender, string error)
        {
            Application.Current.Dispatcher.Invoke(() =>
            {
                IsConnected = false;
                IsConnecting = false;
                IsNotConnected = true;

                StatusMessage = $"Connection error: {error}";
                StatusBrush = Brushes.Red;
                ConnectionStatusText = "Error";
                ConnectionStatusBrush = Brushes.Red;

                _logger.LogError("Connection error: {Error}", error);
            });
        }

        private void OnScreenFrameReceived(object? sender, ScreenFrame frame)
        {
            try
            {
                // Use BeginInvoke for better performance - don't block the network thread
                Application.Current.Dispatcher.BeginInvoke(() =>
                {
                    try
                    {
                        // Convert frame data to BitmapSource with performance optimization
                        var bitmap = _screenDisplayService.ConvertFrameToBitmap(frame);

                        // Only update if bitmap is valid
                        if (bitmap != null)
                        {
                            RemoteScreenImage = bitmap;
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Failed to convert frame to bitmap");
                    }
                }, System.Windows.Threading.DispatcherPriority.Render);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to process screen frame");
            }
        }

        private void OnStatisticsUpdated(object? sender, EventArgs e)
        {
            Application.Current.Dispatcher.Invoke(() =>
            {
                var stats = _statisticsService.GetCurrentStatistics();
                NetworkStatistics = $"{FormatBytes(stats.BytesSentPerSecond)}/s ↑ {FormatBytes(stats.BytesReceivedPerSecond)}/s ↓";
            });
        }

        private void OnQualityStatusReceived(object? sender, QualityStatusMessage status)
        {
            Application.Current.Dispatcher.Invoke(() =>
            {
                try
                {
                    QualityStatusText = $"Client: {(int)(status.CurrentImageScale * 100)}% scale, " +
                                       $"{status.CurrentFrameRate} FPS, " +
                                       $"CPU: {status.CpuUsage:F1}%";

                    _logger.LogInformation("Quality status updated: {StatusText}", QualityStatusText);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error updating quality status display");
                    QualityStatusText = "Lỗi cập nhật trạng thái";
                }
            });
        }

        private void StartTimers()
        {
            // Update current time every second
            var timer = new System.Windows.Threading.DispatcherTimer
            {
                Interval = TimeSpan.FromSeconds(1)
            };
            timer.Tick += (s, e) => CurrentTime = DateTime.Now.ToString("HH:mm:ss");
            timer.Start();
        }

        private static string FormatBytes(long bytes)
        {
            string[] suffixes = { "B", "KB", "MB", "GB" };
            int counter = 0;
            decimal number = bytes;
            while (Math.Round(number / 1024) >= 1)
            {
                number /= 1024;
                counter++;
            }
            return $"{number:n1} {suffixes[counter]}";
        }
    }

    // Placeholder interfaces for services not yet implemented
    public interface IScreenDisplayService
    {
        BitmapSource ConvertFrameToBitmap(ScreenFrame frame);
    }

    public interface IInputService
    {
        void SetInputEnabled(bool enabled);
    }

    public interface IStatisticsService
    {
        NetworkStatistics GetCurrentStatistics();
    }

    public class NetworkStatistics
    {
        public long BytesSentPerSecond { get; set; }
        public long BytesReceivedPerSecond { get; set; }
        public int FrameRate { get; set; }
        public int Latency { get; set; }
    }

    // Placeholder implementations
    public class ScreenDisplayService : IScreenDisplayService
    {
        private readonly ILogger<ScreenDisplayService> _logger;

        public ScreenDisplayService(ILogger<ScreenDisplayService> logger)
        {
            _logger = logger;
        }

        public BitmapSource ConvertFrameToBitmap(ScreenFrame frame)
        {
            try
            {
                if (frame.Data == null || frame.Data.Length == 0)
                {
                    _logger.LogWarning("Empty frame data received");
                    return BitmapSource.Create(1, 1, 96, 96, PixelFormats.Bgr32, null, new byte[4], 4);
                }

                // Log frame details for debugging
                _logger.LogError("Converting frame: {Width}x{Height}, Format: {Format}, Data size: {DataSize}",
                    frame.Width, frame.Height, frame.Format, frame.Data.Length);

                // Additional validation logging
                if (frame.Width <= 0 || frame.Height <= 0)
                {
                    _logger.LogError("Invalid frame dimensions: {Width}x{Height}", frame.Width, frame.Height);
                    return BitmapSource.Create(1, 1, 96, 96, PixelFormats.Bgr32, null, new byte[4], 4);
                }

                // Log first few bytes of data for debugging
                if (frame.Data.Length >= 12)
                {
                    _logger.LogError("First 12 bytes: {0} {1} {2} {3} {4} {5} {6} {7} {8} {9} {10} {11}",
                        frame.Data[0], frame.Data[1], frame.Data[2], frame.Data[3],
                        frame.Data[4], frame.Data[5], frame.Data[6], frame.Data[7],
                        frame.Data[8], frame.Data[9], frame.Data[10], frame.Data[11]);
                }

                // Determine pixel format based on frame format
                PixelFormat pixelFormat;
                int bytesPerPixel;
                int stride;

                // Force BGR24 format for testing
                _logger.LogError("Original format: {Format}, forcing BGR24", frame.Format);
                pixelFormat = PixelFormats.Bgr24;
                bytesPerPixel = 3;

                stride = frame.Width * bytesPerPixel;
                var expectedSize = stride * frame.Height;

                // Validate data size
                if (frame.Data.Length < expectedSize)
                {
                    _logger.LogWarning("Frame data size mismatch: expected {ExpectedSize}, got {ActualSize}",
                        expectedSize, frame.Data.Length);

                    // Create a new array with the expected size, filled with black
                    var paddedData = new byte[expectedSize];
                    Array.Copy(frame.Data, 0, paddedData, 0, Math.Min(frame.Data.Length, expectedSize));
                    frame.Data = paddedData;
                }

                // Create bitmap source with performance optimizations
                var bitmap = BitmapSource.Create(
                    frame.Width,
                    frame.Height,
                    96, // DPI X
                    96, // DPI Y
                    pixelFormat,
                    null,
                    frame.Data,
                    stride
                );

                // Freeze the bitmap to improve performance and enable cross-thread access
                bitmap.Freeze();

                _logger.LogDebug("Successfully created bitmap: {Width}x{Height}, Format: {Format}, Stride: {Stride}",
                    bitmap.PixelWidth, bitmap.PixelHeight, bitmap.Format, stride);

                // Log first few pixels for debugging (RGB values)
                if (frame.Data.Length >= 9) // At least 3 pixels worth of RGB24 data
                {
                    _logger.LogDebug("First 3 pixels RGB values: ({R1},{G1},{B1}) ({R2},{G2},{B2}) ({R3},{G3},{B3})",
                        frame.Data[0], frame.Data[1], frame.Data[2],
                        frame.Data[3], frame.Data[4], frame.Data[5],
                        frame.Data[6], frame.Data[7], frame.Data[8]);
                }

                return bitmap;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error converting frame to bitmap");
                return BitmapSource.Create(1, 1, 96, 96, PixelFormats.Bgr32, null, new byte[4], 4);
            }
        }
    }

    public class InputService : IInputService
    {
        public void SetInputEnabled(bool enabled)
        {
            // TODO: Implement input control
        }
    }

    public class StatisticsService : IStatisticsService
    {
        public NetworkStatistics GetCurrentStatistics()
        {
            return new NetworkStatistics
            {
                BytesSentPerSecond = 0,
                BytesReceivedPerSecond = 0,
                FrameRate = 0,
                Latency = 0
            };
        }
    }
}
