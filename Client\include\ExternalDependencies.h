#pragma once

// Temporary stub implementations for external dependencies
// In production, these would be replaced with actual library includes

#include <string>
#include <vector>
#include <map>
#include <memory>
#include <functional>

// nlohmann/json stub
namespace nlohmann {
    class json {
    public:
        json() = default;
        json(const std::string& str) {}

        template<typename T>
        T value(const std::string& key, const T& defaultValue) const {
            return defaultValue;
        }

        json operator[](const std::string& key) const {
            return json();
        }

        template<typename T>
        T get() const {
            return T{};
        }

        static json parse(const std::string& str) {
            return json();
        }

        std::string dump() const {
            return "{}";
        }
    };
}

// LZ4 stub
namespace LZ4 {
    inline int compress_default(const char* src, char* dst, int srcSize, int dstCapacity) {
        // Stub implementation - just copy data
        if (srcSize <= dstCapacity) {
            memcpy(dst, src, srcSize);
            return srcSize;
        }
        return 0;
    }

    inline int decompress_safe(const char* src, char* dst, int compressedSize, int dstCapacity) {
        // Stub implementation - just copy data
        if (compressedSize <= dstCapacity) {
            memcpy(dst, src, compressedSize);
            return compressedSize;
        }
        return 0;
    }
}

// WebSocket++ stub
namespace websocketpp {
    namespace config {
        struct asio_client {
            typedef std::string message_type;
            typedef std::string connection_type;
        };
    }

    template<typename Config>
    class client {
    public:
        typedef typename Config::message_type message_ptr;
        typedef typename Config::connection_type connection_ptr;
        typedef std::function<void(connection_ptr)> open_handler;
        typedef std::function<void(connection_ptr)> close_handler;
        typedef std::function<void(connection_ptr, message_ptr)> message_handler;

        void init_asio() {}
        void set_access_channels(int) {}
        void clear_access_channels(int) {}
        void set_error_channels(int) {}
        void clear_error_channels(int) {}

        void set_open_handler(open_handler) {}
        void set_close_handler(close_handler) {}
        void set_message_handler(message_handler) {}

        connection_ptr get_connection(const std::string& uri, std::error_code& ec) {
            ec.clear();
            return connection_ptr();
        }

        void connect(connection_ptr) {}
        void run() {}
        void stop() {}
        void send(connection_ptr, const std::string&, int) {}
        void close(connection_ptr, int, const std::string&) {}
    };

    namespace log {
        enum level {
            none = 0,
            rerror = 1,
            warn = 2,
            access = 4,
            info = 8,
            debug = 16,
            all = 0xffffffff
        };
    }

    namespace frame {
        enum opcode {
            text = 0x1,
            binary = 0x2,
            close = 0x8
        };
    }
}

// OpenSSL stubs (minimal)
namespace ssl {
    class context {
    public:
        enum method {
            sslv23_client
        };

        context(method) {}
        void set_options(long) {}
        void set_verify_mode(int) {}
    };
}

// Note: WebRTC types are defined in Common.h to avoid duplicates
