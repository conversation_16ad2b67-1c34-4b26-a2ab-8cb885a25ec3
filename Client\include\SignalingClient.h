#pragma once

#include "Common.h"
#include <string>
#include <vector>
#include <functional>
#include <memory>
#include <thread>
#include <mutex>
#include <atomic>
#include <queue>

// WebSocket client for signaling
class SignalingClient {
public:
    enum class ConnectionState {
        DISCONNECTED,
        CONNECTING,
        CONNECTED,
        FAILED,
        CLOSED
    };

    // Message types for signaling protocol
    enum class MessageType {
        REGISTER,
        OFFER,
        ANSWER,
        ICE_CANDIDATE,
        PEER_CONNECTED,
        PEER_DISCONNECTED,
        ERROR
    };

    // Signaling message structure
    struct SignalingMessage {
        MessageType type;
        std::string fromPeerId;
        std::string toPeerId;
        std::string data;
        int64_t timestamp;
    };

    // Callback function types
    using OnConnectionStateChangeCallback = std::function<void(ConnectionState)>;
    using OnMessageReceivedCallback = std::function<void(const SignalingMessage&)>;
    using OnPeerConnectedCallback = std::function<void(const std::string&)>;
    using OnPeerDisconnectedCallback = std::function<void(const std::string&)>;
    using OnErrorCallback = std::function<void(const std::string&)>;

    SignalingClient();
    ~SignalingClient();

    // Connection management
    bool connect(const std::string& serverUrl, int port, const std::string& peerId);
    void disconnect();
    bool isConnected() const;
    ConnectionState getConnectionState() const;

    // Message sending
    bool sendOffer(const std::string& toPeerId, const std::string& sdp);
    bool sendAnswer(const std::string& toPeerId, const std::string& sdp);
    bool sendIceCandidate(const std::string& toPeerId, const std::string& candidate, 
                         const std::string& sdpMid, int sdpMLineIndex);
    bool sendMessage(const SignalingMessage& message);

    // Callback setters
    void setOnConnectionStateChange(OnConnectionStateChangeCallback callback);
    void setOnMessageReceived(OnMessageReceivedCallback callback);
    void setOnPeerConnected(OnPeerConnectedCallback callback);
    void setOnPeerDisconnected(OnPeerDisconnectedCallback callback);
    void setOnError(OnErrorCallback callback);

    // Utility
    std::vector<std::string> getConnectedPeers() const;
    std::string getPeerId() const;

private:
    // Configuration
    std::string m_serverUrl;
    int m_port;
    std::string m_peerId;

    // State
    std::atomic<ConnectionState> m_connectionState;
    std::atomic<bool> m_running;

    // Threading
    std::unique_ptr<std::thread> m_connectionThread;
    std::unique_ptr<std::thread> m_messageThread;

    // Synchronization
    mutable std::mutex m_mutex;
    std::mutex m_sendQueueMutex;
    std::mutex m_peersListMutex;

    // Message queue
    std::queue<SignalingMessage> m_sendQueue;
    std::vector<std::string> m_connectedPeers;

    // WebSocket handle (platform-specific)
    void* m_websocketHandle;

    // Callbacks
    OnConnectionStateChangeCallback m_onConnectionStateChange;
    OnMessageReceivedCallback m_onMessageReceived;
    OnPeerConnectedCallback m_onPeerConnected;
    OnPeerDisconnectedCallback m_onPeerDisconnected;
    OnErrorCallback m_onError;

    // Private methods
    void connectionThreadFunction();
    void messageThreadFunction();
    bool initializeWebSocket();
    void cleanupWebSocket();
    bool sendRawMessage(const std::string& message);
    void handleReceivedMessage(const std::string& message);
    void processSendQueue();
    void updateConnectionState(ConnectionState newState);
    void addConnectedPeer(const std::string& peerId);
    void removeConnectedPeer(const std::string& peerId);

    // Message serialization/deserialization
    std::string serializeMessage(const SignalingMessage& message);
    SignalingMessage deserializeMessage(const std::string& json);
    std::string messageTypeToString(MessageType type);
    MessageType stringToMessageType(const std::string& typeStr);

    // WebSocket callbacks (static functions for C-style callbacks)
    static void onWebSocketOpen(void* userData);
    static void onWebSocketClose(void* userData, int code, const char* reason);
    static void onWebSocketMessage(void* userData, const char* data, size_t length);
    static void onWebSocketError(void* userData, const char* error);

    // Logging
    void logDebug(const std::string& message);
    void logInfo(const std::string& message);
    void logWarning(const std::string& message);
    void logError(const std::string& message);
};

// Utility functions for JSON handling
namespace SignalingUtils {
    std::string createOfferMessage(const std::string& fromPeer, const std::string& toPeer, const std::string& sdp);
    std::string createAnswerMessage(const std::string& fromPeer, const std::string& toPeer, const std::string& sdp);
    std::string createIceCandidateMessage(const std::string& fromPeer, const std::string& toPeer, 
                                        const std::string& candidate, const std::string& sdpMid, int sdpMLineIndex);
    std::string createRegisterMessage(const std::string& peerId);
    std::string createErrorMessage(const std::string& error);
    
    bool parseOfferMessage(const std::string& json, std::string& fromPeer, std::string& sdp);
    bool parseAnswerMessage(const std::string& json, std::string& fromPeer, std::string& sdp);
    bool parseIceCandidateMessage(const std::string& json, std::string& fromPeer, 
                                std::string& candidate, std::string& sdpMid, int& sdpMLineIndex);
}
