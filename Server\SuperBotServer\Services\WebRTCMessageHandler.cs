using Microsoft.Extensions.Logging;
using SuperBotServer.Models;
using System.Text.Json;
using System.IO;

namespace SuperBotServer.Services
{
    /// <summary>
    /// Handles incoming WebRTC messages and routes them appropriately
    /// </summary>
    public class WebRTCMessageHandler
    {
        private readonly ILogger<WebRTCMessageHandler> _logger;
        private readonly ICompressionService _compressionService;
        private readonly ISecurityService _securityService;

        // Events for different message types
        public event EventHandler<(string peerId, ScreenFrame frame)>? ScreenFrameReceived;
        public event EventHandler<(string peerId, MouseEventData mouseEvent)>? MouseEventReceived;
        public event EventHandler<(string peerId, KeyboardEventData keyEvent)>? KeyboardEventReceived;
        public event EventHandler<(string peerId, string text)>? TextReceived;
        public event EventHandler<(string peerId, string text)>? ClipboardTextReceived;
        public event EventHandler<(string peerId, byte[] imageData)>? ClipboardImageReceived;
        public event EventHandler<(string peerId, FileTransferData fileData)>? FileTransferReceived;
        public event EventHandler<(string peerId, QualityControlMessage qualityControl)>? QualityControlReceived;

        public WebRTCMessageHandler(
            ILogger<WebRTCMessageHandler> logger,
            ICompressionService compressionService,
            ISecurityService securityService)
        {
            _logger = logger;
            _compressionService = compressionService;
            _securityService = securityService;
        }

        /// <summary>
        /// Processes incoming WebRTC message data
        /// </summary>
        public async Task ProcessMessageAsync(string peerId, byte[] messageData)
        {
            try
            {
                if (messageData == null || messageData.Length == 0)
                {
                    _logger.LogWarning("Received empty message from {PeerId}", peerId);
                    return;
                }

                // Parse message header
                var header = ParseMessageHeader(messageData);
                if (header == null)
                {
                    _logger.LogError("Failed to parse message header from {PeerId}", peerId);
                    return;
                }

                // Extract payload
                var headerSize = GetHeaderSize();
                if (messageData.Length < headerSize + header.Length)
                {
                    _logger.LogError("Message payload size mismatch from {PeerId}", peerId);
                    return;
                }

                var payload = new byte[header.Length];
                Array.Copy(messageData, headerSize, payload, 0, (int)header.Length);

                // Route message based on type
                await RouteMessageAsync(peerId, header, payload);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing message from {PeerId}", peerId);
            }
        }

        private async Task RouteMessageAsync(string peerId, WebRTCMessageHeader header, byte[] payload)
        {
            try
            {
                switch (header.Type)
                {
                    case WebRTCMessageType.ScreenFrame:
                        await HandleScreenFrameAsync(peerId, payload);
                        break;

                    case WebRTCMessageType.QualityControl:
                        await HandleQualityControlAsync(peerId, payload);
                        break;

                    case WebRTCMessageType.Heartbeat:
                        await HandleHeartbeatAsync(peerId, payload);
                        break;

                    case WebRTCMessageType.ErrorResponse:
                        await HandleErrorResponseAsync(peerId, payload);
                        break;

                    default:
                        // Try to parse as JSON for other message types
                        await HandleJsonMessageAsync(peerId, payload);
                        break;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error routing message type {MessageType} from {PeerId}", header.Type, peerId);
            }
        }

        private async Task HandleScreenFrameAsync(string peerId, byte[] payload)
        {
            try
            {
                var frame = DeserializeScreenFrame(payload);
                if (frame != null)
                {
                    _logger.LogDebug("Received screen frame from {PeerId}: {Width}x{Height}",
                        peerId, frame.Width, frame.Height);

                    ScreenFrameReceived?.Invoke(this, (peerId, frame));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error handling screen frame from {PeerId}", peerId);
            }
        }

        private async Task HandleQualityControlAsync(string peerId, byte[] payload)
        {
            try
            {
                var qualityControl = JsonSerializer.Deserialize<QualityControlMessage>(payload);
                if (qualityControl != null)
                {
                    _logger.LogDebug("Received quality control from {PeerId}: {ResizePercentage}% resize",
                        peerId, qualityControl.ResizePercentage);

                    QualityControlReceived?.Invoke(this, (peerId, qualityControl));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error handling quality control from {PeerId}", peerId);
            }
        }

        private async Task HandleHeartbeatAsync(string peerId, byte[] payload)
        {
            try
            {
                var heartbeatData = System.Text.Encoding.UTF8.GetString(payload);
                _logger.LogDebug("Received heartbeat from {PeerId}: {Data}", peerId, heartbeatData);

                // Heartbeat is typically just for keeping connection alive
                // No specific action needed
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error handling heartbeat from {PeerId}", peerId);
            }
        }

        private async Task HandleErrorResponseAsync(string peerId, byte[] payload)
        {
            try
            {
                var errorMessage = System.Text.Encoding.UTF8.GetString(payload);
                _logger.LogWarning("Received error response from {PeerId}: {Error}", peerId, errorMessage);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error handling error response from {PeerId}", peerId);
            }
        }

        private async Task HandleJsonMessageAsync(string peerId, byte[] payload)
        {
            try
            {
                var jsonString = System.Text.Encoding.UTF8.GetString(payload);
                var jsonDoc = JsonDocument.Parse(jsonString);

                if (jsonDoc.RootElement.TryGetProperty("Type", out var typeProperty))
                {
                    var messageType = typeProperty.GetString();

                    switch (messageType)
                    {
                        case "mouse":
                            await HandleMouseEventAsync(peerId, jsonDoc.RootElement);
                            break;

                        case "keyboard":
                            await HandleKeyboardEventAsync(peerId, jsonDoc.RootElement);
                            break;

                        case "text":
                            await HandleTextEventAsync(peerId, jsonDoc.RootElement);
                            break;

                        case "clipboard_text":
                            await HandleClipboardTextAsync(peerId, jsonDoc.RootElement);
                            break;

                        case "clipboard_image":
                            await HandleClipboardImageAsync(peerId, jsonDoc.RootElement);
                            break;

                        case "file_transfer_start":
                        case "file_chunk":
                        case "file_request":
                            await HandleFileTransferAsync(peerId, jsonDoc.RootElement);
                            break;

                        case "screen_info_request":
                            await HandleScreenInfoRequestAsync(peerId, jsonDoc.RootElement);
                            break;

                        default:
                            _logger.LogWarning("Unknown JSON message type '{MessageType}' from {PeerId}", messageType, peerId);
                            break;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error handling JSON message from {PeerId}", peerId);
            }
        }

        private async Task HandleMouseEventAsync(string peerId, JsonElement element)
        {
            try
            {
                var mouseEvent = new MouseEventData
                {
                    X = element.GetProperty("X").GetInt32(),
                    Y = element.GetProperty("Y").GetInt32(),
                    Button = element.GetProperty("Button").GetInt32(),
                    Pressed = element.GetProperty("Pressed").GetBoolean(),
                    Timestamp = element.GetProperty("Timestamp").GetInt64()
                };

                _logger.LogDebug("Received mouse event from {PeerId}: ({X}, {Y}) button {Button} {State}",
                    peerId, mouseEvent.X, mouseEvent.Y, mouseEvent.Button, mouseEvent.Pressed ? "pressed" : "released");

                MouseEventReceived?.Invoke(this, (peerId, mouseEvent));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error handling mouse event from {PeerId}", peerId);
            }
        }

        private async Task HandleKeyboardEventAsync(string peerId, JsonElement element)
        {
            try
            {
                var keyEvent = new KeyboardEventData
                {
                    KeyCode = element.GetProperty("KeyCode").GetInt32(),
                    Pressed = element.GetProperty("Pressed").GetBoolean(),
                    Modifiers = element.GetProperty("Modifiers").GetInt32(),
                    Timestamp = element.GetProperty("Timestamp").GetInt64()
                };

                _logger.LogDebug("Received keyboard event from {PeerId}: key {KeyCode} {State}",
                    peerId, keyEvent.KeyCode, keyEvent.Pressed ? "pressed" : "released");

                KeyboardEventReceived?.Invoke(this, (peerId, keyEvent));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error handling keyboard event from {PeerId}", peerId);
            }
        }

        private async Task HandleTextEventAsync(string peerId, JsonElement element)
        {
            try
            {
                var text = element.GetProperty("Text").GetString() ?? "";

                _logger.LogDebug("Received text event from {PeerId}: {Text}", peerId, text);
                TextReceived?.Invoke(this, (peerId, text));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error handling text event from {PeerId}", peerId);
            }
        }

        private async Task HandleClipboardTextAsync(string peerId, JsonElement element)
        {
            try
            {
                var text = element.GetProperty("Text").GetString() ?? "";

                _logger.LogDebug("Received clipboard text from {PeerId}", peerId);
                ClipboardTextReceived?.Invoke(this, (peerId, text));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error handling clipboard text from {PeerId}", peerId);
            }
        }

        private async Task HandleClipboardImageAsync(string peerId, JsonElement element)
        {
            try
            {
                var imageDataBase64 = element.GetProperty("ImageData").GetString() ?? "";
                var imageData = Convert.FromBase64String(imageDataBase64);

                _logger.LogDebug("Received clipboard image from {PeerId}: {Size} bytes", peerId, imageData.Length);
                ClipboardImageReceived?.Invoke(this, (peerId, imageData));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error handling clipboard image from {PeerId}", peerId);
            }
        }

        private async Task HandleFileTransferAsync(string peerId, JsonElement element)
        {
            try
            {
                var fileData = new FileTransferData
                {
                    Type = element.GetProperty("Type").GetString() ?? "",
                    FileName = element.TryGetProperty("FileName", out var fileName) ? fileName.GetString() : null,
                    RemotePath = element.TryGetProperty("RemotePath", out var remotePath) ? remotePath.GetString() : null,
                    LocalPath = element.TryGetProperty("LocalPath", out var localPath) ? localPath.GetString() : null,
                    FileSize = element.TryGetProperty("FileSize", out var fileSize) ? fileSize.GetInt64() : 0,
                    ChunkIndex = element.TryGetProperty("ChunkIndex", out var chunkIndex) ? chunkIndex.GetInt32() : 0,
                    TotalChunks = element.TryGetProperty("TotalChunks", out var totalChunks) ? totalChunks.GetInt32() : 0,
                    Data = element.TryGetProperty("Data", out var data) ? Convert.FromBase64String(data.GetString() ?? "") : null
                };

                _logger.LogDebug("Received file transfer from {PeerId}: {Type}", peerId, fileData.Type);
                FileTransferReceived?.Invoke(this, (peerId, fileData));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error handling file transfer from {PeerId}", peerId);
            }
        }

        private async Task HandleScreenInfoRequestAsync(string peerId, JsonElement element)
        {
            try
            {
                _logger.LogDebug("Received screen info request from {PeerId}", peerId);

                // This would typically trigger sending screen information back to the peer
                // For now, just log the request
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error handling screen info request from {PeerId}", peerId);
            }
        }

        // Helper methods for message parsing
        private WebRTCMessageHeader? ParseMessageHeader(byte[] messageData)
        {
            try
            {
                using var stream = new MemoryStream(messageData);
                using var reader = new BinaryReader(stream);

                return new WebRTCMessageHeader
                {
                    Type = (WebRTCMessageType)reader.ReadUInt16(),
                    Length = reader.ReadUInt32(),
                    Timestamp = reader.ReadInt64(),
                    PeerId = reader.ReadString()
                };
            }
            catch
            {
                return null;
            }
        }

        private int GetHeaderSize()
        {
            // Size calculation: ushort + uint + long + string length prefix
            // This is an approximation - actual size depends on string length
            return sizeof(ushort) + sizeof(uint) + sizeof(long) + sizeof(int);
        }

        private ScreenFrame? DeserializeScreenFrame(byte[] data)
        {
            try
            {
                using var stream = new MemoryStream(data);
                using var reader = new BinaryReader(stream);

                var frame = new ScreenFrame
                {
                    Width = reader.ReadInt32(),
                    Height = reader.ReadInt32(),
                    Format = (ScreenFormat)reader.ReadInt32(),
                    FrameId = reader.ReadUInt32(),
                    IsKeyFrame = reader.ReadBoolean(),
                    Timestamp = reader.ReadInt64() // Timestamp is already long
                };

                var dataLength = reader.ReadInt32();
                frame.Data = reader.ReadBytes(dataLength);

                // Decompress if needed
                if (frame.Data.Length > 0)
                {
                    frame.Data = _compressionService.Decompress(frame.Data);
                }

                return frame;
            }
            catch
            {
                return null;
            }
        }
    }

    // Data structures for different event types
    public class MouseEventData
    {
        public int X { get; set; }
        public int Y { get; set; }
        public int Button { get; set; }
        public bool Pressed { get; set; }
        public long Timestamp { get; set; }
    }

    public class KeyboardEventData
    {
        public int KeyCode { get; set; }
        public bool Pressed { get; set; }
        public int Modifiers { get; set; }
        public long Timestamp { get; set; }
    }

    public class FileTransferData
    {
        public string Type { get; set; } = "";
        public string? FileName { get; set; }
        public string? RemotePath { get; set; }
        public string? LocalPath { get; set; }
        public long FileSize { get; set; }
        public int ChunkIndex { get; set; }
        public int TotalChunks { get; set; }
        public byte[]? Data { get; set; }
    }
}
