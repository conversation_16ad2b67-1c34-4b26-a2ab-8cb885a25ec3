{"name": "superbot-client", "version": "1.0.0", "description": "SuperBot Remote Desktop Client - High-performance C++ client application with WebRTC support", "homepage": "https://github.com/superbot/client", "documentation": "https://docs.superbot.com/client", "license": "MIT", "dependencies": [{"name": "lz4", "version>=": "1.9.4"}, {"name": "<PERSON><PERSON><PERSON>-<PERSON><PERSON>", "version>=": "3.11.0"}, {"name": "websocketpp", "version>=": "0.8.2"}, {"name": "openssl", "version>=": "3.0.0"}], "features": {"compression": {"description": "Enhanced compression support", "dependencies": ["zlib", "zstd"]}, "webrtc": {"description": "WebRTC support for peer-to-peer communication", "dependencies": ["libwebrtc", "boost-asio", "boost-beast"]}, "ssl": {"description": "SSL/TLS support for secure connections", "dependencies": ["openssl"]}, "testing": {"description": "Unit testing framework", "dependencies": ["gtest"]}}, "builtin-baseline": "8f54ef5453e7e76ff01e15988bf243e7247c5eb5"}