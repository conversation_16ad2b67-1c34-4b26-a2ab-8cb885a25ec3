using SuperBotServer.Models;
using System.Net;
using System.Net.Sockets;
using System.Net.Security;
using System.Security.Cryptography.X509Certificates;
using Microsoft.Extensions.Logging;
using System.Runtime.InteropServices;
using System.IO;
using System.IO.Compression;

namespace SuperBotServer.Services
{
    public interface INetworkService : IDisposable
    {
        // Connection management
        Task<bool> ConnectAsync(string address, int port = 7878, CancellationToken cancellationToken = default);
        Task DisconnectAsync();
        bool IsConnected { get; }
        ConnectionInfo? CurrentConnection { get; }

        // Message handling
        Task SendMessageAsync(MessageType type, byte[] payload, MessageFlags flags = MessageFlags.None);
        Task SendMouseEventAsync(int x, int y, int button, bool pressed);
        Task SendKeyboardEventAsync(int keyCode, bool pressed, int modifiers = 0);
        Task SendTextAsync(string text);

        // Screen data
        Task RequestScreenInfoAsync();
        Task<byte[]?> GetLatestScreenFrameAsync();

        // File transfer
        Task<bool> SendFileAsync(string localPath, string remotePath, IProgress<double>? progress = null);
        Task<bool> ReceiveFileAsync(string remotePath, string localPath, IProgress<double>? progress = null);

        // Clipboard
        Task SendClipboardTextAsync(string text);
        Task SendClipboardImageAsync(byte[] imageData);

        // Configuration
        void SetCompressionEnabled(bool enabled);
        void SetEncryptionEnabled(bool enabled);
        void SetHeartbeatInterval(int milliseconds);

        // Quality Control
        Task SendQualityControlAsync(QualityControlMessage qualityControl);
        Task RequestQualityStatusAsync();

        // Statistics
        long BytesSent { get; }
        long BytesReceived { get; }
        int MessagesSent { get; }
        int MessagesReceived { get; }
        int CurrentLatency { get; }
        void ResetStatistics();

        // Compression statistics
        CompressionStats GetCompressionStatistics();

        // Events
        event EventHandler<ConnectionInfo>? ConnectionEstablished;
        event EventHandler? ConnectionLost;
        event EventHandler<string>? ConnectionError;
        event EventHandler<ScreenFrame>? ScreenFrameReceived;
        event EventHandler<string>? ClipboardTextReceived;
        event EventHandler<byte[]>? ClipboardImageReceived;
        event EventHandler<(string operation, double progress)>? FileTransferProgress;
        event EventHandler? StatisticsUpdated;
        event EventHandler<QualityStatusMessage>? QualityStatusReceived;
    }

    public class NetworkService : INetworkService
    {
        private readonly ILogger<NetworkService> _logger;
        private readonly ISecurityService _securityService;
        private readonly ICompressionService _compressionService;
        private TcpClient? _tcpClient;
        private SslStream? _sslStream;
        private NetworkStream? _networkStream;
        private CancellationTokenSource? _cancellationTokenSource;
        private Task? _receiveTask;
        private readonly object _sendLock = new();
        private readonly Queue<byte[]> _sendQueue = new();
        private bool _disposed;

        // Configuration
        private bool _compressionEnabled = true;
        private bool _encryptionEnabled = false; // Temporarily disable for debugging
        private int _heartbeatInterval = 30000; // 30 seconds

        // Statistics
        private long _bytesSent;
        private long _bytesReceived;
        private int _messagesSent;
        private int _messagesReceived;
        private int _currentLatency;
        private DateTime _lastHeartbeat;

        // Connection state
        private ConnectionInfo? _currentConnection;
        private readonly Timer _heartbeatTimer;
        private readonly Timer _statisticsTimer;

        public NetworkService(ILogger<NetworkService> logger, ISecurityService securityService, ICompressionService compressionService)
        {
            _logger = logger;
            _securityService = securityService;
            _compressionService = compressionService;

            _heartbeatTimer = new Timer(SendHeartbeat, null, Timeout.Infinite, Timeout.Infinite);
            _statisticsTimer = new Timer(UpdateStatistics, null, 1000, 1000); // Update every second
        }

        public bool IsConnected => _tcpClient?.Connected == true && _currentConnection?.Status == ConnectionStatus.Connected;
        public ConnectionInfo? CurrentConnection => _currentConnection;

        public long BytesSent => _bytesSent;
        public long BytesReceived => _bytesReceived;
        public int MessagesSent => _messagesSent;
        public int MessagesReceived => _messagesReceived;
        public int CurrentLatency => _currentLatency;

        public async Task<bool> ConnectAsync(string address, int port = 7878, CancellationToken cancellationToken = default)
        {
            try
            {
                if (IsConnected)
                {
                    await DisconnectAsync();
                }

                // Debug: Log struct sizes
                _logger.LogInformation("MessageHeader size: {Size} bytes", Marshal.SizeOf<MessageHeader>());
                _logger.LogInformation("Expected header size (C++): 24 bytes");

                _logger.LogInformation("Connecting to {Address}:{Port}", address, port);

                _currentConnection = new ConnectionInfo
                {
                    Address = address,
                    Port = port,
                    Status = ConnectionStatus.Connecting,
                    ConnectionTime = DateTime.Now
                };

                _cancellationTokenSource = new CancellationTokenSource();
                _tcpClient = new TcpClient();

                // Set connection timeout
                using var timeoutCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
                timeoutCts.CancelAfter(TimeSpan.FromSeconds(10));

                await _tcpClient.ConnectAsync(address, port, timeoutCts.Token);
                _networkStream = _tcpClient.GetStream();

                // Setup SSL if encryption is enabled
                if (_encryptionEnabled)
                {
                    _sslStream = new SslStream(_networkStream, false, ValidateServerCertificate);
                    await _sslStream.AuthenticateAsClientAsync(address);
                }

                // Perform authentication handshake
                if (!await PerformAuthenticationAsync())
                {
                    throw new InvalidOperationException("Authentication failed");
                }

                _currentConnection.Status = ConnectionStatus.Connected;
                _currentConnection.ConnectionTime = DateTime.Now;

                // Start receiving messages
                _receiveTask = Task.Run(() => ReceiveMessagesAsync(_cancellationTokenSource.Token));

                // Start heartbeat
                _heartbeatTimer.Change(_heartbeatInterval, _heartbeatInterval);

                _logger.LogInformation("Successfully connected to {Address}:{Port}", address, port);
                ConnectionEstablished?.Invoke(this, _currentConnection);

                // Send initial quality control settings after successful connection
                await SendInitialQualityControlAsync();

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to connect to {Address}:{Port}", address, port);

                if (_currentConnection != null)
                {
                    _currentConnection.Status = ConnectionStatus.Error;
                    _currentConnection.ErrorMessage = ex.Message;
                }

                await DisconnectAsync();
                ConnectionError?.Invoke(this, ex.Message);
                return false;
            }
        }

        public async Task DisconnectAsync()
        {
            try
            {
                _logger.LogInformation("Disconnecting from remote host");

                // Stop timers
                _heartbeatTimer.Change(Timeout.Infinite, Timeout.Infinite);

                // Cancel operations
                _cancellationTokenSource?.Cancel();

                // Wait for receive task to complete
                if (_receiveTask != null)
                {
                    await _receiveTask.WaitAsync(TimeSpan.FromSeconds(5));
                }

                // Close streams and client
                _sslStream?.Close();
                _networkStream?.Close();
                _tcpClient?.Close();

                if (_currentConnection != null)
                {
                    _currentConnection.Status = ConnectionStatus.Disconnected;
                }

                ConnectionLost?.Invoke(this, EventArgs.Empty);
                _logger.LogInformation("Disconnected from remote host");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during disconnect");
            }
            finally
            {
                _sslStream?.Dispose();
                _networkStream?.Dispose();
                _tcpClient?.Dispose();
                _cancellationTokenSource?.Dispose();

                _sslStream = null;
                _networkStream = null;
                _tcpClient = null;
                _cancellationTokenSource = null;
                _receiveTask = null;
            }
        }

        // Event declarations
        public event EventHandler<ConnectionInfo>? ConnectionEstablished;
        public event EventHandler? ConnectionLost;
        public event EventHandler<string>? ConnectionError;
        public event EventHandler<ScreenFrame>? ScreenFrameReceived;
        public event EventHandler<string>? ClipboardTextReceived;
        public event EventHandler<byte[]>? ClipboardImageReceived;
        public event EventHandler<(string operation, double progress)>? FileTransferProgress;
        public event EventHandler? StatisticsUpdated;
        public event EventHandler<QualityStatusMessage>? QualityStatusReceived;

        // Message sending methods
        public async Task SendMessageAsync(MessageType type, byte[] payload, MessageFlags flags = MessageFlags.None)
        {
            try
            {
                if (!IsConnected) return;

                var message = CreateMessage(type, payload, flags);
                var stream = _encryptionEnabled ? (Stream)_sslStream! : _networkStream!;

                await stream.WriteAsync(message, 0, message.Length);
                await stream.FlushAsync();

                _bytesSent += (uint)message.Length;
                _messagesSent++;

                _logger.LogDebug("Sent message: {Type}, Length: {Length}", type, message.Length);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send message: {Type}", type);
                throw;
            }
        }

        public Task SendMouseEventAsync(int x, int y, int button, bool pressed) => throw new NotImplementedException();
        public Task SendKeyboardEventAsync(int keyCode, bool pressed, int modifiers = 0) => throw new NotImplementedException();
        public Task SendTextAsync(string text) => throw new NotImplementedException();
        public Task RequestScreenInfoAsync() => throw new NotImplementedException();
        public Task<byte[]?> GetLatestScreenFrameAsync() => throw new NotImplementedException();

        public async Task SendQualityControlAsync(QualityControlMessage qualityControl)
        {
            try
            {
                _logger.LogInformation("Sending quality control to client: Scale={Scale}%, Resize={Resize}, FPS={FPS}, Compression={Compression}",
                    (int)(qualityControl.ImageScalePercent * 100), qualityControl.ResizeEnabled, qualityControl.FrameRate, qualityControl.CompressionEnabled);

                // Serialize quality control message
                var payload = SerializeQualityControl(qualityControl);
                await SendMessageAsync(MessageType.QualityControl, payload, MessageFlags.Priority);

                _logger.LogDebug("Quality control message sent successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send quality control message");
                throw;
            }
        }

        public async Task RequestQualityStatusAsync()
        {
            try
            {
                _logger.LogDebug("Requesting quality status from client");

                // Send empty payload to request status
                await SendMessageAsync(MessageType.QualityStatus, Array.Empty<byte>(), MessageFlags.Priority);

                _logger.LogDebug("Quality status request sent");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to request quality status");
                throw;
            }
        }

        private byte[] CreateMessage(MessageType type, byte[] payload, MessageFlags flags)
        {
            var header = new MessageHeader
            {
                Magic = ProtocolConstants.PROTOCOL_MAGIC,
                Length = (uint)(Marshal.SizeOf<MessageHeader>() + payload.Length),
                Type = (ushort)type,
                Sequence = GetNextSequenceNumber(),
                Flags = (ushort)flags
            };

            var message = new byte[header.Length];
            var headerBytes = StructToBytes(header);

            Array.Copy(headerBytes, 0, message, 0, headerBytes.Length);
            if (payload.Length > 0)
            {
                Array.Copy(payload, 0, message, headerBytes.Length, payload.Length);
            }

            return message;
        }

        private byte[] StructToBytes<T>(T structure) where T : struct
        {
            var size = Marshal.SizeOf<T>();
            var bytes = new byte[size];
            var handle = GCHandle.Alloc(bytes, GCHandleType.Pinned);
            try
            {
                Marshal.StructureToPtr(structure, handle.AddrOfPinnedObject(), false);
            }
            finally
            {
                handle.Free();
            }
            return bytes;
        }

        private uint _sequenceNumber = 1;
        private uint GetNextSequenceNumber() => Interlocked.Increment(ref _sequenceNumber);

        private static uint SwapBytes(uint value)
        {
            return ((value & 0x000000FF) << 24) |
                   ((value & 0x0000FF00) << 8) |
                   ((value & 0x00FF0000) >> 8) |
                   ((value & 0xFF000000) >> 24);
        }

        private async Task ProcessScreenFrameAsync(byte[] payload, MessageFlags messageFlags = MessageFlags.None)
        {
            try
            {
                _logger.LogDebug("Processing screen frame, payload size: {Size}", payload.Length);

                // Calculate minimum frame header size based on client serialization format:
                // monitorId (4) + region (16) + format (4) + isFullFrame (1) + timestamp (8) + isCompressed (1) + originalSize (4) + dataSize (4) = 42 bytes
                const int MinFrameHeaderSize = 42;

                if (payload.Length < MinFrameHeaderSize)
                {
                    _logger.LogWarning("Screen frame payload too small: {Size}, minimum required: {MinSize}",
                        payload.Length, MinFrameHeaderSize);
                    return;
                }

                // Log first bytes for debugging
                var headerBytes = payload.Take(Math.Min(48, payload.Length)).ToArray();
                _logger.LogDebug("Frame header bytes: {Bytes}",
                    string.Join(" ", headerBytes.Select(b => $"0x{b:X2}")));

                // Check if this looks like a message header instead of frame header
                if (payload.Length >= 4)
                {
                    var possibleMagic = BitConverter.ToUInt32(payload, 0);
                    if (possibleMagic == ProtocolConstants.PROTOCOL_MAGIC)
                    {
                        _logger.LogError("Received message header instead of frame header - protocol parsing error");
                        return;
                    }
                }

                // Parse frame header according to client serialization format
                int offset = 0;

                // monitorId (4 bytes)
                var monitorId = BitConverter.ToInt32(payload, offset);
                offset += 4;

                // region (16 bytes: x, y, width, height)
                var regionX = BitConverter.ToInt32(payload, offset);
                offset += 4;
                var regionY = BitConverter.ToInt32(payload, offset);
                offset += 4;
                var width = BitConverter.ToInt32(payload, offset);
                offset += 4;
                var height = BitConverter.ToInt32(payload, offset);
                offset += 4;

                // format (4 bytes)
                var format = BitConverter.ToInt32(payload, offset);
                offset += 4;

                // isFullFrame (1 byte)
                var isFullFrame = payload[offset] != 0;
                offset += 1;

                // timestamp (8 bytes)
                var timestamp = BitConverter.ToInt64(payload, offset);
                offset += 8;

                // isCompressed (1 byte)
                var isCompressed = payload[offset] != 0;
                offset += 1;

                // originalSize (4 bytes) - for decompression
                var originalSize = BitConverter.ToInt32(payload, offset);
                offset += 4;

                // dataSize (4 bytes)
                var dataSize = BitConverter.ToInt32(payload, offset);
                offset += 4;

                // Debug: Log parsed values
                _logger.LogDebug("Parsed frame header:");
                _logger.LogDebug("  MonitorId: {MonitorId}", monitorId);
                _logger.LogDebug("  Region: ({X}, {Y}, {Width}, {Height})", regionX, regionY, width, height);
                _logger.LogDebug("  Format: {Format}", format);
                _logger.LogDebug("  IsFullFrame: {IsFullFrame}", isFullFrame);
                _logger.LogDebug("  Timestamp: {Timestamp}", timestamp);
                _logger.LogDebug("  IsCompressed: {IsCompressed}", isCompressed);
                _logger.LogDebug("  OriginalSize: {OriginalSize}", originalSize);
                _logger.LogDebug("  DataSize: {DataSize}", dataSize);
                _logger.LogDebug("  Header size: {HeaderSize} bytes", offset);

                // Validate original size consistency
                if (isCompressed && originalSize > 0)
                {
                    // Perform basic sanity checks on original size
                    var expectedMinSize = width * height; // Minimum 1 byte per pixel
                    var expectedMaxSize = width * height * 4; // Maximum 4 bytes per pixel (RGBA)

                    if (originalSize < expectedMinSize || originalSize > expectedMaxSize)
                    {
                        _logger.LogWarning("Original size {OriginalSize} seems inconsistent with frame dimensions {Width}x{Height} (expected range: {MinSize}-{MaxSize})",
                            originalSize, width, height, expectedMinSize, expectedMaxSize);
                    }
                    else
                    {
                        _logger.LogDebug("Original size {OriginalSize} is consistent with frame dimensions {Width}x{Height}",
                            originalSize, width, height);
                    }
                }

                // Detect if we need to swap bytes (endianness mismatch)
                bool needsByteSwap = false;
                if (width <= 0 || width > 10000 || height <= 0 || height > 10000 || dataSize < 0 || dataSize > 100 * 1024 * 1024)
                {
                    _logger.LogDebug("Detected possible endianness mismatch, trying byte swap");

                    // Try byte-swapped values for all fields
                    var swappedMonitorId = (int)SwapBytes((uint)monitorId);
                    var swappedRegionX = (int)SwapBytes((uint)regionX);
                    var swappedRegionY = (int)SwapBytes((uint)regionY);
                    var swappedWidth = (int)SwapBytes((uint)width);
                    var swappedHeight = (int)SwapBytes((uint)height);
                    var swappedFormat = (int)SwapBytes((uint)format);
                    var swappedOriginalSize = (int)SwapBytes((uint)originalSize);
                    var swappedDataSize = (int)SwapBytes((uint)dataSize);

                    _logger.LogDebug("Swapped values - MonitorId: {MonitorId}, Region: ({X}, {Y}, {Width}, {Height}), Format: {Format}, OriginalSize: {OriginalSize}, DataSize: {DataSize}",
                        swappedMonitorId, swappedRegionX, swappedRegionY, swappedWidth, swappedHeight, swappedFormat, swappedOriginalSize, swappedDataSize);

                    // Check if swapped values are more reasonable
                    if (swappedWidth > 0 && swappedWidth <= 10000 &&
                        swappedHeight > 0 && swappedHeight <= 10000 &&
                        swappedDataSize > 0 && swappedDataSize <= 100 * 1024 * 1024)
                    {
                        needsByteSwap = true;
                        monitorId = swappedMonitorId;
                        regionX = swappedRegionX;
                        regionY = swappedRegionY;
                        width = swappedWidth;
                        height = swappedHeight;
                        format = swappedFormat;
                        originalSize = swappedOriginalSize;
                        dataSize = swappedDataSize;

                        // Swap timestamp bytes too
                        var timestampBytes = BitConverter.GetBytes(timestamp);
                        Array.Reverse(timestampBytes);
                        timestamp = BitConverter.ToInt64(timestampBytes, 0);

                        _logger.LogDebug("Using byte-swapped values");
                    }
                }

                // Validate values to prevent overflow
                if (width <= 0 || width > 10000)
                {
                    _logger.LogError("Invalid width: {Width}", width);
                    return;
                }

                if (height <= 0 || height > 10000)
                {
                    _logger.LogError("Invalid height: {Height}", height);
                    return;
                }

                if (dataSize < 0 || dataSize > 100 * 1024 * 1024) // Max 100MB
                {
                    _logger.LogError("Invalid data size: {DataSize}", dataSize);
                    return;
                }

                if (format < 0 || format > 10)
                {
                    _logger.LogError("Invalid format: {Format}", format);
                    return;
                }

                _logger.LogDebug("Screen frame: {Width}x{Height}, format: {Format}, data size: {DataSize}, compressed: {IsCompressed}",
                    width, height, format, dataSize, isCompressed);

                // Check if we have enough data (using correct header size)
                var expectedTotalSize = offset + dataSize; // offset is the actual header size (38 bytes)
                if (payload.Length < expectedTotalSize)
                {
                    _logger.LogError("Screen frame payload incomplete. Expected: {Expected}, Got: {Actual}, Missing: {Missing} bytes",
                        expectedTotalSize, payload.Length, expectedTotalSize - payload.Length);

                    // Log detailed breakdown
                    _logger.LogError("Frame header size: {HeaderSize} bytes, Expected data size: {DataSize} bytes, Total expected: {Total} bytes",
                        offset, dataSize, expectedTotalSize);

                    // This is a critical error - the message parsing should have caught this
                    _logger.LogError("CRITICAL: Message parser allowed incomplete frame data through - this indicates a parsing bug");
                    return;
                }

                // Additional safety check for data size consistency
                var actualDataSize = payload.Length - offset;
                if (dataSize != actualDataSize)
                {
                    _logger.LogWarning("Data size mismatch: Header says {HeaderDataSize}, but actual payload has {ActualDataSize} bytes",
                        dataSize, actualDataSize);

                    // Use the smaller of the two to prevent buffer overrun
                    dataSize = Math.Min(dataSize, actualDataSize);
                    _logger.LogWarning("Using corrected data size: {CorrectedDataSize}", dataSize);
                }

                // Extract image data safely using correct offset
                var rawImageData = new byte[dataSize];
                if (dataSize > 0)
                {
                    Array.Copy(payload, offset, rawImageData, 0, dataSize);
                }

                // Validate data integrity by calculating checksum of the entire payload
                // The checksum in message header should match the checksum of the serialized frame data
                _logger.LogDebug("Validating frame data integrity...");
                var calculatedChecksum = CalculateCRC32(payload);
                _logger.LogDebug("Calculated payload checksum: 0x{Checksum:X8}", calculatedChecksum);

                // Check if data is compressed and decompress if needed
                byte[] imageData = rawImageData;

                // Check compression status from both message flags and frame header
                bool messageMarkedAsCompressed = messageFlags.HasFlag(MessageFlags.Compressed);
                _logger.LogDebug("Compression status - Message flag: {MessageFlag}, Frame header: {FrameHeader}, OriginalSize: {OriginalSize}, Data size: {Size}",
                    messageMarkedAsCompressed, isCompressed, originalSize, rawImageData.Length);

                // Use frame header compression flag as primary indicator
                if (isCompressed)
                {
                    if (originalSize > 0)
                    {
                        _logger.LogDebug("Frame is compressed with known original size, decompressing image data of size: {Size} to original size: {OriginalSize}",
                            rawImageData.Length, originalSize);
                        try
                        {
                            // Use the overload with known original size for better performance and accuracy
                            imageData = _compressionService.Decompress(rawImageData, originalSize);
                            _logger.LogDebug("Successfully decompressed to size: {Size} (expected: {Expected})",
                                imageData.Length, originalSize);

                            // Strict validation of decompressed size
                            if (imageData.Length != originalSize)
                            {
                                _logger.LogError("CRITICAL: Decompressed size mismatch: got {Actual}, expected {Expected} - this indicates data corruption",
                                    imageData.Length, originalSize);

                                // This is a critical error that suggests data corruption
                                // Try fallback decompression without size hint
                                _logger.LogWarning("Attempting fallback decompression without size hint due to size mismatch");
                                imageData = _compressionService.Decompress(rawImageData);
                                _logger.LogInformation("Fallback decompression resulted in size: {Size}", imageData.Length);
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, "Failed to decompress LZ4 data with known original size {OriginalSize}, attempting fallback", originalSize);
                            try
                            {
                                // Fallback to decompression without size hint
                                imageData = _compressionService.Decompress(rawImageData);
                                _logger.LogWarning("Fallback decompression without size hint successful, size: {Size} (expected: {Expected})",
                                    imageData.Length, originalSize);
                            }
                            catch (Exception ex2)
                            {
                                _logger.LogError(ex2, "All decompression attempts failed, using raw compressed data as-is");
                                imageData = rawImageData;
                            }
                        }
                    }
                    else
                    {
                        _logger.LogWarning("Frame marked as compressed but originalSize is 0 - client may not be sending original size correctly");
                        try
                        {
                            // Try decompression without size hint
                            imageData = _compressionService.Decompress(rawImageData);
                            _logger.LogDebug("Decompression without size hint successful, size: {Size}", imageData.Length);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, "Decompression without size hint failed, using raw data");
                            imageData = rawImageData;
                        }
                    }
                }
                else
                {
                    _logger.LogDebug("Frame is not compressed, using raw data of size: {Size}", rawImageData.Length);
                    imageData = rawImageData;
                }

                // Create ScreenFrame object with all parsed data
                var screenFrame = new ScreenFrame
                {
                    MonitorId = monitorId,
                    X = regionX,
                    Y = regionY,
                    Width = width,
                    Height = height,
                    Format = (ScreenFormat)format,
                    Data = imageData,
                    Timestamp = timestamp,
                    IsFullFrame = isFullFrame
                };

                // Frame processing completed - no longer saving frames to disk

                // Fire event for UI to update
                ScreenFrameReceived?.Invoke(this, screenFrame);

                // Log comprehensive data integrity summary (reduced frequency for performance)
                static int frameLogCounter = 0;
                if (++frameLogCounter % 30 == 0) // Log every 30 frames (1 second at 30 FPS)
                {
                    _logger.LogInformation("=== FRAME DATA INTEGRITY SUMMARY (Frame #{Counter}) ===", frameLogCounter);
                    _logger.LogInformation("Payload: {PayloadSize} bytes, Header: {HeaderSize} bytes", payload.Length, offset);
                    _logger.LogInformation("Image data: {ImageDataSize} -> {ProcessedDataSize} bytes", rawImageData.Length, imageData.Length);
                    _logger.LogInformation("Dimensions: {Width}x{Height}, Compressed: {IsCompressed}", width, height, isCompressed);
                    if (isCompressed && originalSize > 0)
                    {
                        var compressionRatio = (double)rawImageData.Length / originalSize;
                        _logger.LogInformation("Compression: {OriginalSize} -> {CompressedSize} bytes (ratio: {Ratio:F3})",
                            originalSize, rawImageData.Length, compressionRatio);
                    }
                    _logger.LogInformation("Checksum: 0x{Checksum:X8}, Endianness: {Endianness}",
                        calculatedChecksum, needsByteSwap ? "Swapped" : "Native");
                    _logger.LogInformation("=================================================");
                }
                else
                {
                    // Minimal logging for performance
                    _logger.LogDebug("Frame processed: {Width}x{Height}, {DataSize} bytes, compressed: {IsCompressed}",
                        width, height, imageData.Length, isCompressed);
                }

                _logger.LogDebug("Screen frame processed, saved, and event fired");

                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing screen frame");
            }
        }
        public Task<bool> SendFileAsync(string localPath, string remotePath, IProgress<double>? progress = null) => throw new NotImplementedException();
        public Task<bool> ReceiveFileAsync(string remotePath, string localPath, IProgress<double>? progress = null) => throw new NotImplementedException();
        public Task SendClipboardTextAsync(string text) => throw new NotImplementedException();
        public Task SendClipboardImageAsync(byte[] imageData) => throw new NotImplementedException();
        public void SetCompressionEnabled(bool enabled) => _compressionEnabled = enabled;
        public void SetEncryptionEnabled(bool enabled) => _encryptionEnabled = enabled;
        public void SetHeartbeatInterval(int milliseconds) => _heartbeatInterval = milliseconds;
        public void ResetStatistics() { _bytesSent = _bytesReceived = _messagesSent = _messagesReceived = 0; }
        public CompressionStats GetCompressionStatistics() => _compressionService.GetStatistics();

        // Private helper methods
        private bool ValidateServerCertificate(object sender, X509Certificate? certificate, X509Chain? chain, SslPolicyErrors sslPolicyErrors) => true;

        private async Task<bool> PerformAuthenticationAsync()
        {
            try
            {
                _logger.LogInformation("Starting authentication handshake");

                // Don't send anything initially - wait for client to send first message
                _logger.LogInformation("Waiting for client authentication request");

                // For now, just return true without sending anything
                await Task.Delay(100); // Small delay to ensure connection is stable

                _logger.LogInformation("Authentication completed successfully");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Authentication failed");
                return false;
            }
        }
        private void SendHeartbeat(object? state) { }
        private void UpdateStatistics(object? state) => StatisticsUpdated?.Invoke(this, EventArgs.Empty);

        private bool ValidateMessageHeader(MessageHeader header)
        {
            // Check magic number
            if (header.Magic != ProtocolConstants.PROTOCOL_MAGIC)
            {
                _logger.LogError("Invalid magic number: 0x{Magic:X8}", header.Magic);
                return false;
            }

            // Check version - be more lenient with version checking
            if (header.Version != ProtocolConstants.PROTOCOL_VERSION_NUM && header.Version != 0)
            {
                _logger.LogWarning("Version mismatch: got 0x{Version:X4}, expected 0x{Expected:X4}",
                    header.Version, ProtocolConstants.PROTOCOL_VERSION_NUM);
                // Don't fail on version mismatch, just warn
                // Version 0 might be due to uninitialized field, which we'll accept for now
            }
            else if (header.Version == 0)
            {
                _logger.LogDebug("Version field is 0, possibly uninitialized - accepting message");
            }

            // Check message type
            var messageType = (MessageType)header.Type;
            if (!Enum.IsDefined(typeof(MessageType), messageType))
            {
                _logger.LogError("Unknown message type: 0x{Type:X4}", header.Type);
                return false;
            }

            // Check length bounds
            var headerSize = Marshal.SizeOf<MessageHeader>();
            if (header.Length < headerSize)
            {
                _logger.LogError("Message length too small: {Length}, minimum: {MinLength}",
                    header.Length, headerSize);
                return false;
            }

            if (header.Length > ProtocolConstants.MAX_MESSAGE_SIZE)
            {
                _logger.LogError("Message length too large: {Length}, maximum: {MaxLength}",
                    header.Length, ProtocolConstants.MAX_MESSAGE_SIZE);
                return false;
            }

            return true;
        }

        private async Task ReceiveMessagesAsync(CancellationToken cancellationToken)
        {
            var buffer = new byte[ProtocolConstants.RECEIVE_BUFFER_SIZE];
            var messageBuffer = new List<byte>();
            var consecutiveFailures = 0;
            const int maxConsecutiveFailures = 10;

            try
            {
                while (!cancellationToken.IsCancellationRequested && IsConnected)
                {
                    var stream = _encryptionEnabled ? (Stream)_sslStream! : _networkStream!;
                    var bytesRead = await stream.ReadAsync(buffer, 0, buffer.Length, cancellationToken);

                    if (bytesRead == 0)
                    {
                        _logger.LogWarning("Connection closed by remote host");
                        break;
                    }

                    _logger.LogDebug("Received {BytesRead} bytes, total buffer size: {BufferSize}",
                        bytesRead, messageBuffer.Count + bytesRead);

                    messageBuffer.AddRange(buffer.Take(bytesRead));
                    _bytesReceived += (uint)bytesRead;

                    // Process complete messages
                    var messagesProcessed = 0;
                    while (messageBuffer.Count >= Marshal.SizeOf<MessageHeader>())
                    {
                        var initialBufferSize = messageBuffer.Count;

                        if (TryParseMessage(messageBuffer, out var header, out var payload))
                        {
                            _messagesReceived++;
                            messagesProcessed++;
                            consecutiveFailures = 0; // Reset failure counter on success

                            _logger.LogDebug("Processing message {MessageCount}: Type={Type}, Length={Length}, PayloadSize={PayloadSize}",
                                messagesProcessed, (MessageType)header.Type, header.Length, payload.Length);

                            await ProcessReceivedMessage(header, payload);

                            // Remove processed message from buffer
                            var messageSize = (int)header.Length;
                            if (messageSize <= messageBuffer.Count)
                            {
                                messageBuffer.RemoveRange(0, messageSize);
                                _logger.LogDebug("Removed {MessageSize} bytes from buffer, remaining: {Remaining}",
                                    messageSize, messageBuffer.Count);
                            }
                            else
                            {
                                _logger.LogError("Message size {MessageSize} exceeds buffer size {BufferSize}",
                                    messageSize, messageBuffer.Count);
                                messageBuffer.Clear(); // Clear corrupted buffer
                                break;
                            }
                        }
                        else
                        {
                            // Check if buffer size didn't change - might indicate parsing issue
                            if (messageBuffer.Count == initialBufferSize)
                            {
                                consecutiveFailures++;
                                _logger.LogDebug("Parse failed, consecutive failures: {Failures}", consecutiveFailures);

                                if (consecutiveFailures >= maxConsecutiveFailures)
                                {
                                    _logger.LogError("Too many consecutive parse failures ({Failures}), clearing buffer to recover",
                                        consecutiveFailures);

                                    // Log buffer content for debugging
                                    var bufferPreview = string.Join(" ", messageBuffer.Take(Math.Min(32, messageBuffer.Count))
                                        .Select(b => $"0x{b:X2}"));
                                    _logger.LogError("Buffer preview (first 32 bytes): {BufferPreview}", bufferPreview);

                                    messageBuffer.Clear();
                                    consecutiveFailures = 0;
                                }
                            }
                            break; // Wait for more data
                        }
                    }

                    if (messagesProcessed > 0)
                    {
                        _logger.LogDebug("Processed {MessageCount} messages in this iteration", messagesProcessed);
                    }

                    // Prevent buffer from growing too large
                    if (messageBuffer.Count > ProtocolConstants.MAX_MESSAGE_SIZE)
                    {
                        _logger.LogError("Message buffer too large ({Size} bytes), clearing to prevent memory issues",
                            messageBuffer.Count);
                        messageBuffer.Clear();
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in receive loop");
                ConnectionError?.Invoke(this, ex.Message);
            }
        }

        private bool TryParseMessage(List<byte> buffer, out MessageHeader header, out byte[] payload)
        {
            header = default;
            payload = Array.Empty<byte>();

            var headerSize = Marshal.SizeOf<MessageHeader>();
            _logger.LogDebug("MessageHeader size: {HeaderSize} bytes", headerSize);

            if (buffer.Count < headerSize)
                return false;

            // Parse header
            var headerBytes = buffer.Take(Marshal.SizeOf<MessageHeader>()).ToArray();
            var handle = GCHandle.Alloc(headerBytes, GCHandleType.Pinned);
            try
            {
                header = Marshal.PtrToStructure<MessageHeader>(handle.AddrOfPinnedObject());
            }
            finally
            {
                handle.Free();
            }

            // Debug: Log raw bytes
            _logger.LogDebug("Raw header bytes: {Bytes}",
                string.Join(" ", headerBytes.Select(b => $"0x{b:X2}")));

            // Log parsed header values before endianness correction
            _logger.LogDebug("Parsed header (before endianness correction): Magic=0x{Magic:X8}, Length={Length}, Type=0x{Type:X4}, Sequence={Sequence}, Flags=0x{Flags:X4}, Checksum=0x{Checksum:X8}, Version=0x{Version:X4}, Reserved=0x{Reserved:X4}",
                header.Magic, header.Length, header.Type, header.Sequence, header.Flags, header.Checksum, header.Version, header.Reserved);

            // Validate magic number (check both byte orders)
            var swappedMagic = SwapBytes(header.Magic);
            if (header.Magic != ProtocolConstants.PROTOCOL_MAGIC && swappedMagic != ProtocolConstants.PROTOCOL_MAGIC)
            {
                _logger.LogError("Invalid protocol magic number: 0x{Magic:X8}, expected: 0x{Expected:X8}",
                    header.Magic, ProtocolConstants.PROTOCOL_MAGIC);
                _logger.LogError("Byte-swapped magic: 0x{SwappedMagic:X8}", swappedMagic);
                return false;
            }

            // If we got byte-swapped magic, fix the header
            if (header.Magic != ProtocolConstants.PROTOCOL_MAGIC && swappedMagic == ProtocolConstants.PROTOCOL_MAGIC)
            {
                _logger.LogWarning("Received byte-swapped magic number, correcting endianness");
                header.Magic = swappedMagic;
                header.Length = SwapBytes(header.Length);
                header.Type = (ushort)SwapBytes((uint)header.Type);
                header.Sequence = SwapBytes(header.Sequence);
                header.Flags = (ushort)SwapBytes((uint)header.Flags);
                header.Checksum = SwapBytes(header.Checksum);
                header.Version = (ushort)SwapBytes((uint)header.Version);
                header.Reserved = (ushort)SwapBytes((uint)header.Reserved);

                _logger.LogDebug("Corrected header: Magic=0x{Magic:X8}, Length={Length}, Type=0x{Type:X4}, Sequence={Sequence}, Flags=0x{Flags:X4}, Checksum=0x{Checksum:X8}, Version=0x{Version:X4}, Reserved=0x{Reserved:X4}",
                    header.Magic, header.Length, header.Type, header.Sequence, header.Flags, header.Checksum, header.Version, header.Reserved);
            }
            else
            {
                _logger.LogDebug("Header endianness is correct (native)");
            }

            // Validate the header
            if (!ValidateMessageHeader(header))
            {
                return false;
            }

            // Check if we have complete message
            if (buffer.Count < header.Length)
            {
                _logger.LogDebug("Incomplete message: have {BufferSize} bytes, need {MessageLength} bytes",
                    buffer.Count, header.Length);
                return false;
            }

            // Extract payload safely
            var payloadSize = (int)(header.Length - headerSize);

            _logger.LogDebug("Payload extraction: MessageLength={MessageLength}, HeaderSize={HeaderSize}, CalculatedPayloadSize={PayloadSize}, BufferSize={BufferSize}",
                header.Length, headerSize, payloadSize, buffer.Count);

            if (payloadSize > 0)
            {
                // Double-check we have enough data for the payload
                if (buffer.Count < headerSize + payloadSize)
                {
                    _logger.LogError("Buffer underrun: need {Required} bytes for payload, have {Available} bytes",
                        headerSize + payloadSize, buffer.Count);
                    return false;
                }

                payload = buffer.Skip(headerSize).Take(payloadSize).ToArray();

                _logger.LogDebug("Extracted payload: RequestedSize={RequestedSize}, ActualSize={ActualSize}",
                    payloadSize, payload.Length);

                // Verify we got the expected amount of data
                if (payload.Length != payloadSize)
                {
                    _logger.LogError("Payload size mismatch: expected {Expected}, got {Actual}",
                        payloadSize, payload.Length);
                    return false;
                }

                // Validate checksum if present
                if (header.Checksum != 0)
                {
                    var calculatedChecksum = CalculateCRC32(payload);
                    _logger.LogDebug("Checksum validation: expected=0x{Expected:X8}, calculated=0x{Calculated:X8}, payload_size={PayloadSize}",
                        header.Checksum, calculatedChecksum, payload.Length);

                    if (calculatedChecksum != header.Checksum)
                    {
                        _logger.LogWarning("Checksum mismatch: expected 0x{Expected:X8}, calculated 0x{Calculated:X8} - continuing anyway for debugging",
                            header.Checksum, calculatedChecksum);
                        // Don't fail on checksum mismatch for now, just warn
                        // return false;
                    }
                    else
                    {
                        _logger.LogDebug("Checksum validation passed: 0x{Checksum:X8}", calculatedChecksum);
                    }
                }
            }

            _logger.LogDebug("Successfully parsed message: Type={Type}, Length={Length}, PayloadSize={PayloadSize}",
                (MessageType)header.Type, header.Length, payloadSize);

            return true;
        }

        private async Task ProcessReceivedMessage(MessageHeader header, byte[] payload)
        {
            try
            {
                var messageType = (MessageType)header.Type;
                var messageFlags = (MessageFlags)header.Flags;
                _logger.LogDebug("Received message: {Type}, Length: {Length}, Sequence: {Sequence}, Flags: {Flags}",
                    messageType, header.Length, header.Sequence, messageFlags);

                switch (messageType)
                {
                    case MessageType.ScreenFrame:
                        await ProcessScreenFrameAsync(payload, messageFlags);
                        break;
                    case MessageType.Heartbeat:
                        _logger.LogDebug("Heartbeat received");
                        break;
                    case MessageType.AuthResponse:
                        _logger.LogDebug("Auth response received");
                        break;
                    case MessageType.QualityStatus:
                        await ProcessQualityStatusAsync(payload);
                        break;
                    default:
                        _logger.LogWarning("Unknown message type: {Type}", messageType);
                        break;
                }

                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing message");
            }
        }

        private uint CalculateCRC32(byte[] data)
        {
            // Simple CRC32 implementation
            const uint polynomial = 0xEDB88320;
            uint crc = 0xFFFFFFFF;

            foreach (byte b in data)
            {
                crc ^= b;
                for (int i = 0; i < 8; i++)
                {
                    if ((crc & 1) != 0)
                        crc = (crc >> 1) ^ polynomial;
                    else
                        crc >>= 1;
                }
            }

            return ~crc;
        }

        private byte[] SerializeQualityControl(QualityControlMessage qualityControl)
        {
            // Serialize QualityControlMessage to match client's expected format
            var data = new List<byte>();

            // imageScalePercent (4 bytes - float)
            data.AddRange(BitConverter.GetBytes(qualityControl.ImageScalePercent));

            // resizeEnabled (1 byte - bool)
            data.Add((byte)(qualityControl.ResizeEnabled ? 1 : 0));

            // compressionQuality (4 bytes - int)
            data.AddRange(BitConverter.GetBytes(qualityControl.CompressionQuality));

            // compressionEnabled (1 byte - bool)
            data.Add((byte)(qualityControl.CompressionEnabled ? 1 : 0));

            // frameRate (4 bytes - int)
            data.AddRange(BitConverter.GetBytes(qualityControl.FrameRate));

            _logger.LogDebug("Serialized QualityControl: Scale={Scale}, Resize={Resize}, Quality={Quality}, Compression={Compression}, FPS={FPS}, Size={Size} bytes",
                qualityControl.ImageScalePercent, qualityControl.ResizeEnabled, qualityControl.CompressionQuality,
                qualityControl.CompressionEnabled, qualityControl.FrameRate, data.Count);

            return data.ToArray();
        }

        private QualityStatusMessage DeserializeQualityStatus(byte[] data)
        {
            if (data.Length < 24) // Expected size: 4+1+4+4+4+8 = 25 bytes minimum
            {
                _logger.LogWarning("Quality status data too small: {Size} bytes", data.Length);
                return new QualityStatusMessage();
            }

            var offset = 0;
            var status = new QualityStatusMessage();

            // currentImageScale (4 bytes - float)
            status.CurrentImageScale = BitConverter.ToSingle(data, offset);
            offset += 4;

            // resizeEnabled (1 byte - bool)
            status.ResizeEnabled = data[offset] != 0;
            offset += 1;

            // currentFrameRate (4 bytes - int)
            status.CurrentFrameRate = BitConverter.ToInt32(data, offset);
            offset += 4;

            // averageFrameSize (4 bytes - int) - Note: this is read-only in the model
            var averageFrameSize = BitConverter.ToInt32(data, offset);
            offset += 4;

            // cpuUsage (4 bytes - float)
            status.CpuUsage = BitConverter.ToSingle(data, offset);
            offset += 4;

            // timestamp (8 bytes - long)
            status.Timestamp = BitConverter.ToInt64(data, offset);

            _logger.LogDebug("Deserialized QualityStatus: Scale={Scale}, Resize={Resize}, FPS={FPS}, FrameSize={FrameSize}, CPU={CPU}%, Timestamp={Timestamp}",
                status.CurrentImageScale, status.ResizeEnabled, status.CurrentFrameRate, averageFrameSize, status.CpuUsage, status.Timestamp);

            return status;
        }

        private async Task ProcessQualityStatusAsync(byte[] payload)
        {
            try
            {
                _logger.LogDebug("Processing quality status message, payload size: {Size}", payload.Length);

                if (payload.Length == 0)
                {
                    _logger.LogDebug("Empty quality status payload - client may be requesting current settings");
                    return;
                }

                var qualityStatus = DeserializeQualityStatus(payload);

                _logger.LogInformation("Received quality status from client: Scale={Scale}%, Resize={Resize}, FPS={FPS}, CPU={CPU}%",
                    (int)(qualityStatus.CurrentImageScale * 100), qualityStatus.ResizeEnabled,
                    qualityStatus.CurrentFrameRate, qualityStatus.CpuUsage);

                // Fire event for UI to update
                QualityStatusReceived?.Invoke(this, qualityStatus);

                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing quality status message");
            }
        }

        private async Task SendInitialQualityControlAsync()
        {
            try
            {
                // Wait a bit for connection to stabilize
                await Task.Delay(1000);

                // Send default quality control settings
                var defaultQuality = new QualityControlMessage
                {
                    ImageScalePercent = 0.5f,  // 50% scale by default
                    ResizeEnabled = true,
                    CompressionQuality = 80,
                    CompressionEnabled = true,
                    FrameRate = 30
                };

                _logger.LogInformation("Sending initial quality control settings to client");
                await SendQualityControlAsync(defaultQuality);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send initial quality control settings");
                // Don't throw - this is not critical for connection
            }
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                DisconnectAsync().Wait(TimeSpan.FromSeconds(5));
                _heartbeatTimer.Dispose();
                _statisticsTimer.Dispose();
                _disposed = true;
            }
        }
    }
}
