using System.ComponentModel;
using System.Net;
using System.Windows.Input;
using System.Windows.Media;

namespace SuperBotServer.Models
{
    public enum ConnectionStatus
    {
        Disconnected,
        Connecting,
        Connected,
        Reconnecting,
        Error
    }

    public enum MessageType : ushort
    {
        // Authentication
        AuthRequest = 0x0101,
        AuthResponse = 0x0102,

        // Screen sharing
        ScreenInfo = 0x0201,
        ScreenFrame = 0x0202,

        // Input control
        MouseEvent = 0x0301,
        KeyboardEvent = 0x0302,

        // File transfer
        FileTransferRequest = 0x0401,
        FileChunk = 0x0402,

        // System
        Heartbeat = 0x0501,
        ClipboardSync = 0x0502,
        ErrorResponse = 0x0503,

        // Quality control
        QualityControl = 0x0504,
        QualityStatus = 0x0505
    }

    public enum MessageFlags : ushort
    {
        None = 0x0000,
        Compressed = 0x0001,
        Encrypted = 0x0002,
        Priority = 0x0004,
        Fragmented = 0x0008
    }

    public class ConnectionInfo : INotifyPropertyChanged
    {
        private string _address = string.Empty;
        private int _port = 7878;
        private ConnectionStatus _status = ConnectionStatus.Disconnected;
        private DateTime _connectionTime;
        private DateTime _lastActivity;
        private string _clientVersion = string.Empty;
        private string _clientOS = string.Empty;
        private long _bytesSent;
        private long _bytesReceived;
        private int _frameRate;
        private int _latency;
        private string _errorMessage = string.Empty;
        private string _protocol = "TCP";

        public string Address
        {
            get => _address;
            set
            {
                if (_address != value)
                {
                    _address = value;
                    OnPropertyChanged(nameof(Address));
                    OnPropertyChanged(nameof(DisplayName));
                }
            }
        }

        public int Port
        {
            get => _port;
            set
            {
                if (_port != value)
                {
                    _port = value;
                    OnPropertyChanged(nameof(Port));
                    OnPropertyChanged(nameof(DisplayName));
                }
            }
        }

        public ConnectionStatus Status
        {
            get => _status;
            set
            {
                if (_status != value)
                {
                    _status = value;
                    OnPropertyChanged(nameof(Status));
                    OnPropertyChanged(nameof(StatusText));
                    OnPropertyChanged(nameof(StatusBrush));
                    OnPropertyChanged(nameof(IsConnected));
                    OnPropertyChanged(nameof(IsConnecting));
                    OnPropertyChanged(nameof(IsDisconnected));
                }
            }
        }

        public DateTime ConnectionTime
        {
            get => _connectionTime;
            set
            {
                if (_connectionTime != value)
                {
                    _connectionTime = value;
                    OnPropertyChanged(nameof(ConnectionTime));
                    OnPropertyChanged(nameof(ConnectionDuration));
                }
            }
        }

        public DateTime LastActivity
        {
            get => _lastActivity;
            set
            {
                if (_lastActivity != value)
                {
                    _lastActivity = value;
                    OnPropertyChanged(nameof(LastActivity));
                }
            }
        }

        public string ClientVersion
        {
            get => _clientVersion;
            set
            {
                if (_clientVersion != value)
                {
                    _clientVersion = value;
                    OnPropertyChanged(nameof(ClientVersion));
                }
            }
        }

        public string ClientOS
        {
            get => _clientOS;
            set
            {
                if (_clientOS != value)
                {
                    _clientOS = value;
                    OnPropertyChanged(nameof(ClientOS));
                }
            }
        }

        public long BytesSent
        {
            get => _bytesSent;
            set
            {
                if (_bytesSent != value)
                {
                    _bytesSent = value;
                    OnPropertyChanged(nameof(BytesSent));
                    OnPropertyChanged(nameof(BytesSentFormatted));
                }
            }
        }

        public long BytesReceived
        {
            get => _bytesReceived;
            set
            {
                if (_bytesReceived != value)
                {
                    _bytesReceived = value;
                    OnPropertyChanged(nameof(BytesReceived));
                    OnPropertyChanged(nameof(BytesReceivedFormatted));
                }
            }
        }

        public int FrameRate
        {
            get => _frameRate;
            set
            {
                if (_frameRate != value)
                {
                    _frameRate = value;
                    OnPropertyChanged(nameof(FrameRate));
                }
            }
        }

        public int Latency
        {
            get => _latency;
            set
            {
                if (_latency != value)
                {
                    _latency = value;
                    OnPropertyChanged(nameof(Latency));
                }
            }
        }

        public string ErrorMessage
        {
            get => _errorMessage;
            set
            {
                if (_errorMessage != value)
                {
                    _errorMessage = value;
                    OnPropertyChanged(nameof(ErrorMessage));
                }
            }
        }

        public string Protocol
        {
            get => _protocol;
            set
            {
                if (_protocol != value)
                {
                    _protocol = value;
                    OnPropertyChanged(nameof(Protocol));
                }
            }
        }

        // Computed properties
        public string DisplayName => $"{Address}:{Port}";

        public string StatusText => Status switch
        {
            ConnectionStatus.Disconnected => "Disconnected",
            ConnectionStatus.Connecting => "Connecting...",
            ConnectionStatus.Connected => "Connected",
            ConnectionStatus.Reconnecting => "Reconnecting...",
            ConnectionStatus.Error => $"Error: {ErrorMessage}",
            _ => "Unknown"
        };

        public bool IsConnected => Status == ConnectionStatus.Connected;
        public bool IsConnecting => Status == ConnectionStatus.Connecting || Status == ConnectionStatus.Reconnecting;
        public bool IsDisconnected => Status == ConnectionStatus.Disconnected;

        public TimeSpan ConnectionDuration => Status == ConnectionStatus.Connected
            ? DateTime.Now - ConnectionTime
            : TimeSpan.Zero;

        public string BytesSentFormatted => FormatBytes(BytesSent);
        public string BytesReceivedFormatted => FormatBytes(BytesReceived);

        // UI-specific properties
        public Brush StatusBrush => Status switch
        {
            ConnectionStatus.Connected => Brushes.Green,
            ConnectionStatus.Connecting => Brushes.Orange,
            ConnectionStatus.Reconnecting => Brushes.Orange,
            ConnectionStatus.Error => Brushes.Red,
            ConnectionStatus.Disconnected => Brushes.Gray,
            _ => Brushes.Gray
        };

        // Command for disconnect button - will be set by ViewModel
        public ICommand? DisconnectCommand { get; set; }

        private static string FormatBytes(long bytes)
        {
            string[] suffixes = { "B", "KB", "MB", "GB", "TB" };
            int counter = 0;
            decimal number = bytes;
            while (Math.Round(number / 1024) >= 1)
            {
                number /= 1024;
                counter++;
            }
            return $"{number:n1} {suffixes[counter]}";
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    public class ScreenInfo
    {
        public List<MonitorInfo> Monitors { get; set; } = new();
        public int ColorDepth { get; set; }
        public int RefreshRate { get; set; }
    }

    public class MonitorInfo
    {
        public int Id { get; set; }
        public int Width { get; set; }
        public int Height { get; set; }
        public int X { get; set; }
        public int Y { get; set; }
        public bool Primary { get; set; }
        public string Name { get; set; } = string.Empty;
    }

    public enum ScreenFormat
    {
        RGB24 = 0,
        RGB32 = 1,
        BGR24 = 2,
        BGR32 = 3,
        H264 = 4
    }

    public class ScreenFrame
    {
        public int MonitorId { get; set; }
        public int X { get; set; }
        public int Y { get; set; }
        public int Width { get; set; }
        public int Height { get; set; }
        public byte[] Data { get; set; } = Array.Empty<byte>();
        public bool IsFullFrame { get; set; }
        public long Timestamp { get; set; }
        public ScreenFormat Format { get; set; } = ScreenFormat.RGB24;

        // Additional properties for WebRTC compatibility
        public uint FrameId { get; set; }
        public bool IsKeyFrame { get; set; }
        public bool IsCompressed { get; set; }
        public uint OriginalSize { get; set; }
    }

    public class QualityControlMessage
    {
        public float ImageScalePercent { get; set; } = 1.0f; // 0.1 to 2.0 (10% to 200%)
        public bool ResizeEnabled { get; set; } = true;      // Enable/disable resizing
        public int CompressionQuality { get; set; } = 80;    // 1-100 compression quality
        public bool CompressionEnabled { get; set; } = true; // Enable/disable compression
        public int FrameRate { get; set; } = 30;             // Target frame rate (1-60)

        // Alias properties for compatibility
        public int ResizePercentage
        {
            get => (int)(ImageScalePercent * 100);
            set => ImageScalePercent = value / 100.0f;
        }

        public int CompressionLevel
        {
            get => CompressionQuality;
            set => CompressionQuality = value;
        }

        public bool EnableAdaptiveQuality { get; set; } = true;
    }

    public class QualityStatusMessage
    {
        public float CurrentImageScale { get; set; }    // Current scale percentage
        public bool ResizeEnabled { get; set; }         // Current resize state
        public int CurrentFrameRate { get; set; }       // Current frame rate
        public int AverageFrameSize { get; }            // Average frame size in bytes
        public float CpuUsage { get; set; }             // CPU usage percentage
        public long Timestamp { get; set; }             // Status timestamp
    }

    public class InputEvent
    {
        public string Type { get; set; } = string.Empty;
        public int X { get; set; }
        public int Y { get; set; }
        public int Button { get; set; }
        public int KeyCode { get; set; }
        public int Modifiers { get; set; }
        public string Text { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; } = DateTime.Now;
    }
}
