#pragma once

#ifndef WIN32_LEAN_AND_MEAN
#define WIN32_LEAN_AND_MEAN
#endif

#ifndef NOMINMAX
#define NOMINMAX
#endif

#include <windows.h>
#include <winsock2.h>
#include <ws2tcpip.h>
#include <wincrypt.h>

// C++ standard library
#include <memory>
#include <vector>
#include <string>
#include <functional>
#include <thread>
#include <mutex>
#include <condition_variable>
#include <queue>
#include <atomic>
#include <chrono>
#include <map>
#include <set>
#include <iostream>
#include <fstream>
#include <sstream>

// Version information
#define SUPERBOT_VERSION_MAJOR 1
#define SUPERBOT_VERSION_MINOR 0
#define SUPERBOT_VERSION_PATCH 0
#define SUPERBOT_VERSION_STRING "1.0.0"

// Protocol constants
#define PROTOCOL_MAGIC 0x53424F54 // "SBOT"
#define PROTOCOL_VERSION "1.0"
#define PROTOCOL_VERSION_NUM 0x0100 // Version 1.0 as uint16
#define DEFAULT_PORT 7878
#define MAX_MESSAGE_SIZE (64 * 1024 * 1024) // 64MB

// Additional Windows constants
#ifndef CALG_SHA_256
#define CALG_SHA_256 0x0000800c
#endif
#define HEARTBEAT_INTERVAL 30000 // 30 seconds

// Message types
enum class MessageType : uint16_t
{
    // Authentication
    AUTH_REQUEST = 0x0101,
    AUTH_RESPONSE = 0x0102,

    // Screen sharing
    SCREEN_INFO = 0x0201,
    SCREEN_FRAME = 0x0202,

    // Input control
    MOUSE_INPUT = 0x0301,
    KEYBOARD_INPUT = 0x0302,

    // File transfer
    FILE_TRANSFER_REQUEST = 0x0401,
    FILE_CHUNK = 0x0402,

    // System
    HEARTBEAT = 0x0501,
    CLIPBOARD_SYNC = 0x0502,
    ERROR_RESPONSE = 0x0503,

    // Quality control
    QUALITY_CONTROL = 0x0504,
    QUALITY_STATUS = 0x0505
};

// Message flags
enum class MessageFlags : uint16_t
{
    NONE = 0x0000,
    COMPRESSED = 0x0001,
    ENCRYPTED = 0x0002,
    PRIORITY = 0x0004,
    FRAGMENTED = 0x0008
};

// Screen capture formats
enum class ScreenFormat
{
    RGB24,
    RGB32,
    BGR24,
    BGR32,
    H264
};

// Input event types
enum class InputType
{
    MOUSE_MOVE,
    MOUSE_CLICK,
    MOUSE_SCROLL,
    KEY_DOWN,
    KEY_UP
};

// Mouse buttons
enum class MouseButton
{
    LEFT = 1,
    RIGHT = 2,
    MIDDLE = 4
};

// Keyboard modifiers
enum class KeyModifier
{
    NONE = 0x00,
    CTRL = 0x01,
    SHIFT = 0x02,
    ALT = 0x04,
    META = 0x08
};

// Error codes
enum class ErrorCode
{
    SUCCESS = 0,
    AUTH_FAILED = 1001,
    INVALID_MESSAGE = 1002,
    UNSUPPORTED_OPERATION = 1003,
    SCREEN_CAPTURE_ERROR = 2001,
    INPUT_INJECTION_ERROR = 3001,
    FILE_TRANSFER_ERROR = 4001,
    NETWORK_ERROR = 5001
};

// Forward declarations
class Application;
class NetworkClient;
class SecurityManager;
class ScreenCapture;
class InputHandler;
class Logger;

// Common structures
struct Point
{
    int x, y;
    Point(int x = 0, int y = 0) : x(x), y(y) {}
};

struct Size
{
    int width, height;
    Size(int w = 0, int h = 0) : width(w), height(h) {}
};

struct Rect
{
    int x, y, width, height;
    Rect(int x = 0, int y = 0, int w = 0, int h = 0) : x(x), y(y), width(w), height(h) {}
};

struct MonitorInfo
{
    int id;
    Rect bounds;
    bool primary;
    int refreshRate;
    std::string name;
};

struct MouseEvent
{
    InputType type;
    Point position;
    MouseButton button;
    int scrollDelta;
    uint32_t modifiers;
};

struct KeyboardEvent
{
    InputType type;
    uint32_t keyCode;
    uint32_t modifiers;
    std::string text;
};

struct ScreenFrame
{
    int monitorId;
    Rect region;
    ScreenFormat format;
    std::vector<uint8_t> data;
    bool isFullFrame;
    uint64_t timestamp;
    bool isCompressed = false; // Track if data is actually compressed
    uint32_t originalSize = 0; // Original size before compression (for decompression)
};

struct QualityControlMessage
{
    float imageScalePercent; // 0.1 to 2.0 (10% to 200%)
    bool resizeEnabled;      // Enable/disable resizing
    int compressionQuality;  // 1-100 compression quality
    bool compressionEnabled; // Enable/disable compression
    int frameRate;           // Target frame rate (1-60)
};

struct QualityStatusMessage
{
    float currentImageScale; // Current scale percentage
    bool resizeEnabled;      // Current resize state
    int currentFrameRate;    // Current frame rate
    int averageFrameSize;    // Average frame size in bytes
    float cpuUsage;          // CPU usage percentage
    uint64_t timestamp;      // Status timestamp
};

// Message header structure
#pragma pack(push, 1)
struct MessageHeader
{
    uint32_t magic;
    uint32_t length;
    uint16_t type;
    uint32_t sequence;
    uint16_t flags;
    uint32_t checksum; // CRC32 checksum of payload
    uint16_t version;  // Protocol version
    uint16_t reserved; // Reserved for future use
};
#pragma pack(pop)

// Utility functions
namespace DataIntegrity
{
    uint32_t calculateCRC32(const uint8_t *data, size_t length);
    bool validateMessageHeader(const MessageHeader &header, size_t payloadSize);
    bool validateScreenFrame(const ScreenFrame &frame);
    bool validateQualityControlMessage(const QualityControlMessage &msg);
    bool validateQualityStatusMessage(const QualityStatusMessage &msg);

    // Endianness conversion
    uint16_t swapBytes16(uint16_t value);
    uint32_t swapBytes32(uint32_t value);
    uint64_t swapBytes64(uint64_t value);

    // Data serialization helpers
    std::vector<uint8_t> serializeScreenFrame(const ScreenFrame &frame);
    bool deserializeScreenFrame(const std::vector<uint8_t> &data, ScreenFrame &frame);
    std::vector<uint8_t> serializeQualityControl(const QualityControlMessage &msg);
    bool deserializeQualityControl(const std::vector<uint8_t> &data, QualityControlMessage &msg);

    // Debug helpers
    void debugStructSizes();
}

// Utility macros
#define SAFE_DELETE(ptr) \
    do                   \
    {                    \
        delete ptr;      \
        ptr = nullptr;   \
    } while (0)
#define SAFE_DELETE_ARRAY(ptr) \
    do                         \
    {                          \
        delete[] ptr;          \
        ptr = nullptr;         \
    } while (0)

// Utility functions
std::string getLogFilePath();

// Logging macros
#define LOG_DEBUG(msg) Logger::instance()->debug(msg)
#define LOG_INFO(msg) Logger::instance()->info(msg)
#define LOG_WARNING(msg) Logger::instance()->warning(msg)
#define LOG_ERROR(msg) Logger::instance()->error(msg)

// Windows-only platform includes (already included above)

// Type aliases
using MessageCallback = std::function<void(MessageType, const std::vector<uint8_t> &)>;
using ErrorCallback = std::function<void(ErrorCode, const std::string &)>;
using ScreenFrameCallback = std::function<void(const ScreenFrame &)>;
using InputEventCallback = std::function<void(const MouseEvent &)>;
using KeyboardEventCallback = std::function<void(const KeyboardEvent &)>;

// Constants for performance tuning
constexpr int MAX_FRAME_RATE = 60;
constexpr int MIN_FRAME_RATE = 5;
constexpr int DEFAULT_FRAME_RATE = 30;
constexpr int COMPRESSION_QUALITY = 80;
constexpr int NETWORK_BUFFER_SIZE = 65536;
constexpr int SCREEN_CAPTURE_TIMEOUT = 100; // milliseconds

// Performance optimization constants
constexpr int ADAPTIVE_FRAME_RATE_ADJUSTMENT = 5;
constexpr int FRAME_SKIP_THRESHOLD_PERCENT = 10;
constexpr float MIN_FRAME_CHANGE_THRESHOLD = 2.0f; // Minimum change percentage to send frame
constexpr int QUALITY_ADJUSTMENT_INTERVAL = 2000; // milliseconds
constexpr int PERFORMANCE_LOG_INTERVAL = 5; // seconds
