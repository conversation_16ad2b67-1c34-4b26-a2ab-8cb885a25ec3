using SuperBotServer.Services;

namespace SuperBotServer.Extensions
{
    /// <summary>
    /// Extension methods for compression services to support async operations
    /// </summary>
    public static class CompressionExtensions
    {
        /// <summary>
        /// Asynchronously compresses data using the compression service
        /// </summary>
        public static async Task<byte[]> CompressAsync(this ICompressionService compressionService, byte[] data)
        {
            return await Task.Run(() => compressionService.Compress(data));
        }

        /// <summary>
        /// Asynchronously decompresses data using the compression service
        /// </summary>
        public static async Task<byte[]> DecompressAsync(this ICompressionService compressionService, byte[] compressedData)
        {
            return await Task.Run(() => compressionService.Decompress(compressedData));
        }

        /// <summary>
        /// Compresses data with quality control
        /// </summary>
        public static async Task<byte[]> CompressWithQualityAsync(this ICompressionService compressionService, 
            byte[] data, int qualityLevel = 5)
        {
            return await Task.Run(() =>
            {
                // Adjust compression based on quality level (1-10, where 1 is fastest, 10 is best compression)
                // This is a placeholder implementation
                return compressionService.Compress(data);
            });
        }

        /// <summary>
        /// Estimates compression ratio without actually compressing
        /// </summary>
        public static double EstimateCompressionRatio(this ICompressionService compressionService, byte[] data)
        {
            if (data == null || data.Length == 0)
                return 1.0;

            // Simple estimation based on data entropy
            var uniqueBytes = data.Distinct().Count();
            var entropy = (double)uniqueBytes / 256.0;
            
            // Estimate compression ratio (this is a rough approximation)
            return Math.Max(0.1, 1.0 - (entropy * 0.7));
        }

        /// <summary>
        /// Determines if data should be compressed based on size and type
        /// </summary>
        public static bool ShouldCompress(this ICompressionService compressionService, byte[] data, int minSize = 1024)
        {
            if (data == null || data.Length < minSize)
                return false;

            // Check if data appears to be already compressed (high entropy)
            var estimatedRatio = compressionService.EstimateCompressionRatio(data);
            return estimatedRatio > 0.2; // Only compress if we expect at least 20% reduction
        }
    }
}
